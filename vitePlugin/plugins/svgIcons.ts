import path from 'node:path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

/**
 * @name SvgIconsPlugin
 * @description 加载SVG文件，自动引入
 */
export function configSvgIconsPlugin() {
  return createSvgIconsPlugin({
    // Specify the icon folder to be cached
    iconDirs: [path.resolve(process.cwd(), 'src/icons')],
    // Specify symbolId format
    symbolId: 'icon-[dir]-[name]',
    //
    // /**
    //  * custom insert position
    //  * @default: body-last
    //  */
    // inject?: 'body-last' | 'body-first'
    //
    // /**
    //  * custom dom id
    //  * @default: __svg__icons__dom__
    //  */
    // customDomId: '__svg__icons__dom__',
  })
}
