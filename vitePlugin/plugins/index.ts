import type { PluginOption } from 'vite'
import vue from '@vitejs/plugin-vue'
import { visualizer } from 'rollup-plugin-visualizer'
import { nodePolyfills } from 'vite-plugin-node-polyfills'
import vueDevTools from 'vite-plugin-vue-devtools'
import { unocssPlugin } from '../../uno.config'
import { AutoImportDeps } from './autoImport'
import { AutoRegistryComponents } from './component'
import { ConfigRestartPlugin } from './restart'
import { configSvgIconsPlugin } from './svgIcons'

const isWebStorm = !!process.env.WebStorm
export function createVitePlugins(): PluginOption[] {
  return [
    vue(),
    /* 原子化CSS插件 */
    unocssPlugin(),
    /* 打包分析 */
    visualizer(),
    // 自动按需引入组件
    AutoRegistryComponents(),
    // 自动按需引入依赖
    AutoImportDeps(),
    /* 监听配置文件改动重启 */
    ConfigRestartPlugin(),
    configSvgIconsPlugin(),
    vueDevTools({ launchEditor: isWebStorm ? 'webstorm' : 'code' }),
    nodePolyfills(),
  ]
}
