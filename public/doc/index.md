# 项目架构方式探讨

## 1. 探讨原因

本项目与传统大数据项目相比有两个不同点:

- 不使用独立域名,直接使用管理端域名
- 没有单独的登录系统, 登录信息直接共用管理端的

## 2.实现方式

### 方案一: 直接在管理端内实现大数据

基于上述的两个不同点,首先想到的则是直接将大数据代码写在管理端内即可,然后增加一个大数据版的Layout,再配置不同路由即可.
这样做上述的两个问题则迎刃而解,
但这样实现弊端也很明显:

- 代码耦合度高,不利于后期维护
- 大数据代码量较大,直接放在管理端内会导致管理端的代码量过大
- 样式冲突,大数据和管理端使用的是同一UI组件库,要实现不同的样式,需要在大数据代码中增加大量的样式覆盖代码

### 方案二: 单独实现大数据

是否可以像开发传统大数据一样,单独开发,而又能够解决上述两个问题呢?

其实是可以的,前端单独开发完成后,交给后端部署时,部署到管理端的子目录下即可.这样第一个问题就解决了,接下来看第二个问题.
如何共用登录信息.

同域名下的所有页面和子项目有个特性,就是他们是共用storage的,也就是说,其实只要将登录信息存在localStorage里,两个系统便实现了共用登录信息.

## 3. 结论

综上所述,我们可以得出结论,大数据项目可以单独开发,而又能够共用登录信息.

# 技术选型

## 1. 前端框架与语言

- **核心框架**：Vue 3.5.x
- **编程语言**：TypeScript 5.8.x
- **构建工具**：Vite 6.3.x

## 2. UI组件与样式

- **UI组件库**：Element Plus 2.9.x (中文本地化)
- **CSS方案**：
  - SASS: CSS预处理器
  - UnoCSS: 原子化CSS框架
  - normalize.css: CSS样式重置

## 3. 状态管理与路由

- **状态管理**：Pinia 3.0.x
- **路由管理**：Vue Router 4.5.x

## 4. 工具库

- **HTTP客户端**：Axios 1.9.0
- **工具库**：
  - @vueuse/core: Vue组合式API工具集
  - dayjs: 轻量级日期处理库
  - mitt: 事件总线
- **地图库**：Leaflet
- **动画库**：animate.css

## 5. 开发与质量保障

- **代码规范**：
  - ESLint 9.x + @antfu/eslint-config
  - Stylelint 16.x
- **测试工具**：Vitest 3.1.x
- **开发增强**：
  - unplugin-auto-import: API自动导入
  - unplugin-vue-components: 组件自动导入
  - vite-plugin-svg-icons: SVG图标处理
  - vite-plugin-vue-devtools: Vue开发工具
  - rollup-plugin-visualizer: 构建分析

## 技术选型说明

### 1.地图

为什么选择Leaflet?

首先我们看一下目前我们有哪些地图可供选择: 高德地图, bigeMap, Leaflet
#### 高德地图: 
- 优点:
  - 功能强大,支持3D地图,支持扩展三维模型
  - 样式美观,可随意自定义样式
  - 文档友好
- 缺点:
  - 收费!!!
  - api使用较为复杂
  - 性能较差

#### bigeMap: 
- 优点:
  - 支持下载离线地图, 相对于网络地图资源,拥有更好的稳定性,以及更快的访问速度
- 缺点:
  - 3D地图支持差
  - 无法完全自定义样式,自定义样式需额外付费
  - 文档不友好,文档内容不全
  - api使用复杂
  - 性能差


#### Leaflet: 
- 优点:
  - 免费开源,拥有丰富的插件生态,功能较为强大
  - 文档友好
  - api使用简单
- 缺点:
  - 无法完全自定义样式
  - 不支持3D地图
