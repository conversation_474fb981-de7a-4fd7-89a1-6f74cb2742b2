<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import type { ContentAddRequest } from '@/api/knowledgeStore/knowledgeStore'
import { MdEditor } from 'md-editor-v3'
import { KnowledgeAPI } from '@/api/knowledgeStore/knowledgeStore'
import PreviewRichText from '@/components/PreviewRichText/PreviewRichText.vue'
import { useAppStore } from '@/store/app'
import 'md-editor-v3/lib/style.css'

const knowledgeBaseId = defineProps<{ id: number }>()

const ratio = computed(() => useAppStore().ratio)

const dialogVisible = defineModel<boolean>('visible', { required: true })
function initForm(): ContentAddRequest {
  return {
    knowledgeBaseId: knowledgeBaseId.id,
    title: '',
    content: '',
    coverImg: '',
    tags: [],
  }
}

const coverImg = ref<string[]>([])
const form = ref<ContentAddRequest>(initForm())
const dialogFormRef = ref<FormInstance>()
const rules = ref({
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
})

// 添加缺失的变量
const loading = ref(false)
const previewVisible = ref(false)

// 列表相关
const contentList = ref([])
const contentListLoading = ref(false)
const contentPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 获取内容列表
async function getContentList() {
  contentListLoading.value = true
  try {
    const { records, total } = await KnowledgeAPI.contentPage({
      KnowledgeBaseId: knowledgeBaseId.id,
      pageNum: contentPagination.value.currentPage,
      pageSize: contentPagination.value.pageSize,
    })
    contentList.value = records
    contentPagination.value.total = total
  }
  finally {
    contentListLoading.value = false
  }
}

// 处理分页大小改变
function handleSizeChange(val: number) {
  contentPagination.value.pageSize = val
  contentPagination.value.currentPage = 1
  getContentList()
}

// 处理页码改变
function handleCurrentChange(val: number) {
  contentPagination.value.currentPage = val
  getContentList()
}

// 预览内容
function handlePreview(content: string) {
  form.value.content = content
  previewVisible.value = true
}

async function handleSubmit() {
  if (!dialogFormRef.value)
    return
  const valid = await dialogFormRef.value.validate()
  if (valid) {
    loading.value = true
    form.value.coverImg = coverImg.value[0]
    try {
      await KnowledgeAPI.addContent(form.value)
      dialogVisible.value = false
      getContentList() // 刷新列表
    }
    finally {
      loading.value = false
    }
  }
}

function handleClose() {
  dialogVisible.value = false
  dialogFormRef.value?.resetFields()
}

// 初始化时获取列表
onMounted(() => {
  getContentList()
})
</script>

<template>
  <el-dialog v-model="dialogVisible" :close-on-click-modal="false" title="知识库内容" :width="1050 * ratio" @close="handleClose">
    <el-scrollbar :height="600 * ratio">
      <el-form ref="dialogFormRef" size="large" :model="form" :rules="rules" :label-width="`${ratio * 80}px`" class="p-30">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input-tag v-model="form.tags" placeholder="请输入标签，按回车确认" />
        </el-form-item>
        <el-form-item label="主图" prop="coverImg">
          <FileUpload v-model="coverImg" accept=".png,.jpg,.jpge" :limit="1" list-type="picture-card" :show-button="false" />
        </el-form-item>
        <el-form-item label="知识内容" prop="content">
          <MdEditor v-model="form.content" />
        </el-form-item>
      </el-form>

      <!-- 内容列表 -->
      <div v-loading="contentListLoading" class="px-30">
        <div class="text-18 font-bold mb-20">内容列表</div>
        <div v-if="contentList.length" class="grid grid-cols-3 gap-25">
          <div v-for="item in contentList" :key="item.id" class="content-card p-20">
            <div class="flex items-center mb-15">
              <img :src="item.coverImg" class="w-60 h-60 object-cover mr-10" />
              <div class="flex-1">
                <el-tooltip :content="item.title" placement="top">
                  <div class="truncate text-16 font-bold">{{ item.title }}</div>
                </el-tooltip>
                <div class="mt-5 flex flex-wrap gap-5">
                  <el-tag v-for="tag in item.tags" :key="tag" size="small">{{ tag }}</el-tag>
                </div>
              </div>
            </div>
            <div class="text-#909090 text-14">最后更新时间：{{ item.modifyTime }}</div>
            <div class="mt-20 flex justify-end gap-10">
              <el-button type="primary" link @click="handlePreview(item.content)">预览</el-button>
              <el-button type="danger" link>删除</el-button>
            </div>
          </div>
        </div>
        <el-empty v-else />

        <!-- 分页 -->
        <div class="flex justify-end mt-20">
          <el-pagination
            v-model:current-page="contentPagination.currentPage"
            v-model:page-size="contentPagination.pageSize"
            :page-sizes="[10, 20, 50, 100, 300]"
            :total="contentPagination.total"
            layout="total, prev, pager, next, sizes, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-scrollbar>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>

  <!-- 预览弹框 -->
  <el-dialog v-model="previewVisible" title="内容预览" :width="800 * ratio">
    <PreviewRichText :content="form.content" />
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.md-editor-footer) {
  height: 50px;
}

.content-card {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}
</style>
