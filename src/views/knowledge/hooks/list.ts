import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { KnowledgeAPI } from '@/api/knowledgeStore/knowledgeStore'
import bingchongfanghai from '@/assets/knowledgeStore/bingchongA.webp'
import nongyedamoxing from '@/assets/knowledgeStore/damoxingA.webp'
import nongyefabu from '@/assets/knowledgeStore/fabuA.webp'
import nongyekeji from '@/assets/knowledgeStore/nongjiA.webp'
import zhuanjia from '@/assets/knowledgeStore/zhuanjiaA.webp'
// import { auth } from '@/store/permisson'

export function useList() {
  const imgMap = {
    1: zhuanjia,
    2: nongyekeji,
    3: bingchongfanghai,
    4: nongyefabu,
    5: nongyedamoxing,
  }

  // 表格数据
  const tableData = ref([])
  const tableLoading = ref(false)

  // 获取列表数据
  const getList = async () => {
    tableLoading.value = true
    try {
      const res = await KnowledgeAPI.list()
      tableData.value = res
    }
    finally {
      tableLoading.value = false
    }
  }

  getList()

  const type = ref<1 | 2 | 3 | 4 | 5>(1)
  const router = useRouter()
  const handleOpen = async (row) => {
    type.value = row.type
    switch (row.type) {
      case 1:
        if (row.articleCount === 0) {
          ElMessage.warning('暂无相关内容')
          return
        }

        router.push(`/knowledge/specialistList?knowledgeBaseId=${row.id}`)
        break
      case 2:
        if (row.articleCount === 0) {
          ElMessage.warning('暂无相关内容')
          return
        }

        router.push(`/knowledge/storeContent?id=${row.id}&title=农业农技`)
        break
      case 3:
        if (row.articleCount === 0) {
          ElMessage.warning('暂无相关内容')
          return
        }

        router.push(`/knowledge/storeContent?id=${row.id}&title=病虫害防治`)
        break
      case 4:
        window.open(row.url)
        break
      case 5:
        router.push('/knowledge/questionAnswering')
        break
    }
  }

  return {
    imgMap,
    tableData,
    tableLoading,
    type,
    handleOpen,
    getList,
  }
}
