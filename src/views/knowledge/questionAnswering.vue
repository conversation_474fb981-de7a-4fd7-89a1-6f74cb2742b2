<script lang="ts" setup>
import { KnowledgeAPI } from '@/api/knowledgeStore/knowledgeStore'
import AvatarAi from '@/assets/knowledgeStore/<EMAIL>'
import AvatarUser from '@/assets/knowledgeStore/<EMAIL>'
import { useUserStore } from '@/store/user'
// import { Delete, Promotion } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import hljs from 'highlight.js'
import { marked } from 'marked'
import { useRoute } from 'vue-router'
import 'highlight.js/styles/github.css'

const userStore = useUserStore()

// 配置 marked
marked.setOptions({
  highlight(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      return hljs.highlight(code, { language: lang }).value
    }

    return hljs.highlightAuto(code).value
  },
  breaks: true,
})

const question = ref('')
const { knowledgeBaseId } = useRoute().query
const loading = ref(false)

// 对话历史
interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  time: string
}

const chatHistory = ref<ChatMessage[]>([])

// 生成当前时间
function getCurrentTime() {
  return new Date().toLocaleTimeString()
}

// 滚动到底部
const messageContainer = ref<HTMLElement>()
function scrollToBottom() {
  nextTick(() => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

// 是否显示打字机效果
const isTyping = ref(true)
const currentTypingText = ref('')
const typingInterval = ref<number | null>(null)

// 打字机效果
function typeWriter(text: string) {
  isTyping.value = true
  currentTypingText.value = ''
  let index = 0

  const type = () => {
    if (index < text.length) {
      currentTypingText.value += text[index]
      index++
      typingInterval.value = window.setTimeout(type, 20)
    }
    else {
      isTyping.value = false
    }
  }

  type()
}

// 提交问题
async function submit() {
  if (!question.value.trim())
    return

  const userQuestion = question.value

  // 添加用户问题到历史记录
  chatHistory.value.push({
    role: 'user',
    content: userQuestion,
    time: getCurrentTime(),
  })

  loading.value = true

  try {
    const res = await KnowledgeAPI.getAnswerWithoutStream(userQuestion, knowledgeBaseId as string)

    // 添加AI回答到历史记录
    chatHistory.value.push({
      role: 'assistant',
      content: res.response,
      time: getCurrentTime(),
    })

    // 添加打字机效果
    typeWriter(res.response)
    scrollToBottom()
    question.value = ''
  }
  catch {
    ElMessage.error('获取回答失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 清空对话
function clearChat() {
  ElMessageBox.confirm('确定要清空所有对话记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    chatHistory.value = []
    ElMessage.success('对话已清空')
  })
}

// 渲染 Markdown
function renderMarkdown(content: string) {
  return marked(content)
}

// 初始化对话
onMounted(() => {
  const welcomeMessage = '您好，很高兴为您服务，有什么问题都可以点击下方对话框向我提问，我会尽力为您解答！'
  chatHistory.value.push({
    role: 'assistant',
    content: welcomeMessage,
    time: getCurrentTime(),
  })
  // 为初始消息添加打字机效果
  typeWriter(welcomeMessage)
})

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (typingInterval.value) {
    clearTimeout(typingInterval.value)
  }
})
</script>

<template>
  <div>
    <div class="chat-container">
      <!-- 对话内容区域 -->
      <el-scrollbar ref="messageContainer" class="message-container">
        <div class="messages">
          <div v-for="(msg, index) in chatHistory" :key="index" :style="{ marginLeft: msg.role === 'user' ? 'auto' : 0 }" class="message-item" :class="[msg.role]">
            <div v-if="msg.role !== 'user'" class="message-avatar">
              <el-avatar :size="40" :src=" AvatarAi">
                AI
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-header" :class="msg.role === 'user' ? 'float-right' : ''">
                <span v-if="msg.role !== 'user'" class="role-name mr-20!">AI助手</span>
                <span class="message-time color-#D3E6DC" :class="msg.role === 'user' ? 'mr-20' : ''">{{ msg.time }}</span>
                <span v-if="msg.role === 'user'" class="role-name">{{ userStore.user.nickname }}</span>
              </div>
              <div v-if="msg.role === 'user'" class="markdown-body" v-html="renderMarkdown(msg.content)" />
              <div v-else class="markdown-body">
                <div v-if="isTyping && index === chatHistory.length - 1" v-html="renderMarkdown(currentTypingText)" />
                <div v-else v-html="renderMarkdown(msg.content)" />
                <div v-if="loading && index === chatHistory.length - 1" class="typing-indicator">
                  <span />
                  <span />
                  <span />
                </div>
              </div>
            </div>

            <div v-if="msg.role === 'user'" class="message-avatar ml-10">
              <el-avatar :size="40" :src="AvatarUser">我</el-avatar>
            </div>
          </div>
        </div>
      </el-scrollbar>

      <!-- 输入区域 -->
      <div class="input-container w-full">
        <!-- <el-button v-if="chatHistory.length > 0" class="clear-btn" type="danger" :icon="Delete" @click="clearChat"> 清空对话 </el-button> -->

        <el-input v-model="question" class="input" :disabled="loading" placeholder="输入您的问题，让我们开始交流吧~" @keyup.enter="submit">
          <template #suffix>
            <el-tooltip v-if="!question" effect="dark" content="请输入您的问题" placement="top">
              <el-button type="primary" class="bg-#12BB75! !w-118 !h-54 -mr-10" disabled @click="submit">发送</el-button>
            </el-tooltip>
            <el-button v-else class="bg-#12BB75! !w-118 !h-54 -mr-10" :loading="loading" type="primary" @click="submit">发送</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input) {
  border: none !important;

  .el-input__wrapper {
    padding: 10px 20px;
    border-color: #11a463 !important;
    border-radius: 6px;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  width: 1380px;
  height: calc(100vh - 130px);
  margin: 0 auto;
  background: url(@/assets/knowledgeStore/<EMAIL>) no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
}

.message-container {
  flex: 1;
  width: 1300px;
  padding: 20px;
  margin: 0 auto;
  overflow-y: auto;

  .empty-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    margin-top: 120px;
  }

  .messages {
    display: flex;
    flex-wrap: wrap;
    padding: 20px 20px 0;
    margin: 0 auto;
  }
}

.message-item {
  display: flex;
  width: 800px;
  margin-bottom: 20px;

  .message-avatar {
    flex-shrink: 0;
    margin-right: 12px;
  }

  .message-content {
    flex: 1;
    max-width: calc(100% - 52px);
    padding: 20px 25px;
    background: rgba(5, 45, 36, 0.5);
    border: 2px solid #2b7f69;
    border-radius: 14px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .message-header {
      margin-bottom: 8px;

      .role-name {
        margin-right: 8px;
        font-weight: 600;
        color: #59e79f;
      }

      .message-time {
        font-size: 12px;
        color: #999;
      }
    }
  }

  &.assistant .message-content {
    background: #0c5848;
    border: 2px solid #2b7f69;
    border-radius: 14px;
  }
}

.input-container {
  width: 100%;
  padding: 30px;
  margin: 0 auto;

  .clear-btn {
    margin-bottom: 12px;
  }
}

:deep(.markdown-body) {
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.6;

  pre {
    padding: 12px;
    margin: 8px 0;
    background: #f6f8fa;
    border-radius: 4px;
  }

  code {
    padding: 2px 4px;
    background: #f6f8fa;
    border-radius: 4px;
  }

  p {
    margin: 8px 0;
  }
}

.typing-indicator {
  display: flex;
  padding: 6px;

  span {
    width: 8px;
    height: 8px;
    margin: 0 2px;
    background-color: #90939b;
    border-radius: 50%;
    animation: typing 1s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 200ms;
    }

    &:nth-child(2) {
      animation-delay: 300ms;
    }

    &:nth-child(3) {
      animation-delay: 400ms;
    }
  }
}

@keyframes typing {
  0% {
    opacity: 0.7;
    transform: translateY(0);
  }

  50% {
    opacity: 0.4;
    transform: translateY(-5px);
  }

  100% {
    opacity: 0.7;
    transform: translateY(0);
  }
}

:deep(.el-input__wrapper) {
  background: linear-gradient(90deg, rgba(22, 40, 38, 0.88), rgba(13, 49, 43, 0.88), rgba(5, 61, 54, 0.88));
  border: 1px solid;
  border-radius: 10px;
  border-image: linear-gradient(90deg, #021f15, #019c79, #000913) 10 10;
  box-shadow: none;

  input {
    color: #ecfff8 !important;
  }
}
</style>
