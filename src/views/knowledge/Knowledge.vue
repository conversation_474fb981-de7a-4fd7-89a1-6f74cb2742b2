<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { useList } from './hooks/list'

// 查询功能块
const formRef = ref<FormInstance>()
const { tableData, tableLoading, imgMap, type, handleOpen } = useList()
</script>

<template>
  <div class="knowledge w-full h-full">
    <!-- 知识库列表 -->
    <div v-loading="tableLoading">
      <div class="card-container">
        <div v-for="item in tableData" :key="item.type" class="card use-bg cursor-pointer w-340 h-462" @click="handleOpen(item)">
          <div class="flex justify-center items-center my-40">
            <img class="w-145 h-180 object-cover" :src="imgMap[item.type]" alt="" />
          </div>

          <div class="text-box px-30 py-35 color-#ECFFF8">
            <div class="store-name text-24 mb-20 font-youShe text-24">{{ item.name }}</div>
            <div class="h-30">
              <div v-if="![4, 5].includes(item.type)" text="16">
                {{ item.type === 1 ? '专家数量' : '内容数量' }}：{{ item.articleCount }}
              </div>
            </div>
            <div class="text-#A2B6AE mt-20" text="14">最后更新时间：{{ item.modifyTime || '--' }}</div>
          </div>
        </div>
        <el-empty v-if="tableData.length === 0" class="mx-auto">
          <template #description>
            <span text="18">暂无知识库内容</span>
          </template>
        </el-empty>
      </div>
    </div>
    <el-empty v-if="!tableData.length && !tableLoading" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 18px;
}

.card-container {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 110px);

  .card {
    height: 462px;
    background: url(@/assets/knowledgeStore/<EMAIL>);
    background-size: 100% 100%;
    border: 1px solid rgba(0, 119, 67, 0.2);
    opacity: 0.7;
    transition: all 0.3s;

    &:hover {
      width: 390px;
      box-shadow: 0 0 20px 1px rgba(0, 119, 67, 0.5);
      opacity: 1;
      transform: scale(1, 1.15);
    }

    .store-name {
      background: linear-gradient(0deg, #9fffcf 0%, #fff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}

.store {
  width: 312px;
  height: 157px !important;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
}
</style>
