<script setup lang="ts">
import { MdPreview } from 'md-editor-v3'
import { useRoute } from 'vue-router'
import { KnowledgeAPI } from '@/api/knowledgeStore/knowledgeStore'
import titleIcon from '@/assets/knowledgeStore/<EMAIL>'
import titleIconA from '@/assets/knowledgeStore/<EMAIL>'

const id = useRoute().query.id
const title = useRoute().query.title || ''

const knowledgeList = ref<any[]>([])
const activeIndex = ref(0)
const loading = ref(false)
const itemData = ref<any>({})
const pagination = reactive({
  pageNum: 1,
  pageSize: 20,
  total: 0,
})
async function getData() {
  loading.value = true
  try {
    const res = await KnowledgeAPI.contentPage({
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      knowledgeBaseId: Number(id),
    })
    knowledgeList.value = res.records
    pagination.total = res.total
    itemData.value = res.records[0]
  }
  finally {
    loading.value = false
  }
}

// 处理分页大小变化
function handleSizeChange(val: number) {
  pagination.pageSize = val
  getData()
}

// 处理页码变化
function handleCurrentChange(val: number) {
  pagination.pageNum = val
  getData()
}

function showDetail(data, index) {
  activeIndex.value = index
  itemData.value = data
}

getData()
</script>

<template>
  <div>
    <div class="flex justify-between px-25 pt-10">
      <div class="left rounded-10 w-460 py-30 px-15 mr-20">
        <div class="left-scroll">
          <el-scrollbar>
            <div
              v-for="(item, index) in knowledgeList"
              :key="item.id"
              :class="index === activeIndex ? 'selected' : ''"
              class="list-item flex items-center cursor-pointer hover:bg-#0F9F6C hover:color-#fff!  mb-8 px-30"
              @click="showDetail(item, index)"
            >
              <img class="w-13 h-16" :src="activeIndex === index ? titleIconA : titleIcon" alt="" />
              <span class="truncate ml-20">{{ item.title }}</span>
            </div>
          </el-scrollbar>
        </div>

        <div class="flex justify-end mt-35">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100, 300]"
            :total="pagination.total"
            background
            layout="total, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <div class="content-side rounded-10 flex-1 ">
        <el-scrollbar>
          <h2 class="text-center mb-28! color-#ECFFF8 mt-45!">{{ itemData.title }}</h2>
          <div class="text-center mb-40 color-#91AFA4" text="14">
            <!-- <span class="mr-20">更新时间：{{ itemData.modifyTime }}</span> -->
            标签：<span v-for="(tag, index) in itemData.tags" :key="index">{{ ` #${tag}` }}</span>
            <span v-if="!itemData.tags || itemData.tags.length === 0">--</span>
          </div>
          <MdPreview class="px-170" :modelValue="itemData.content" />
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.md-editor) {
  background: transparent;

  p {
    color: #ecfff8 !important;
    // text-indent: 2em; // 添加首行缩进
  }
}

.content-side {
  height: calc(100vh - 150px);
  padding: 30px 0;
  background: url(@/assets/knowledgeStore/<EMAIL>) no-repeat;
  background-size: 100% 100%;
}

.left-scroll {
  height: calc(100vh - 250px);
}

.left {
  height: calc(100vh - 200px);
  background: url(@/assets/knowledgeStore/<EMAIL>);
  background-size: 100% 100%;

  .list-item {
    height: 53px;
    color: #adc3ba;
    border: 1px solid #05564c;
  }

  .selected {
    color: #fff;
    background: #0f9f6c;
  }
}
</style>
