<script setup lang="ts">
import { MdPreview } from 'md-editor-v3'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { KnowledgeAPI } from '@/api/knowledgeStore/knowledgeStore'
import { html2Text } from '@/utils/format'
import 'md-editor-v3/lib/style.css'

const specialists = ref([])
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const knowledgeBaseId = useRoute().query.knowledgeBaseId // 默认知识库ID

async function fetchSpecialists() {
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      knowledgeBaseId,
    }
    const response = await KnowledgeAPI.specialistPage(params)
    specialists.value = response.records
    total.value = response.total
  }
  catch (error) {
    console.error('获取专家列表失败:', error)
  }
}

function handleSizeChange(val: number) {
  pageSize.value = val
  pageNum.value = 1
  fetchSpecialists()
}

function handleCurrentChange(val: number) {
  pageNum.value = val
  fetchSpecialists()
}

const dialogVisible = ref(false)
const specialist = ref<any>({})
function showDetail(data) {
  dialogVisible.value = true
  specialist.value = data
}

fetchSpecialists()
</script>

<template>
  <div class="specialist-list">
    <el-scrollbar>
      <div class="specialist-grid">
        <div v-for="item in specialists" :key="item.id" class="specialist-card" @click="showDetail(item)">
          <div class="specialist-content">
            <div class="avatar">
              <Image v-if="item.coverImg" class="avatar-img" :src="item.coverImg" alt="专家头像" />
              <div v-else class="default-avatar-img">
                <img src="@/assets/knowledgeStore/<EMAIL>" alt="" />
              </div>
            </div>
            <div>
              <h3 class="name truncate font-youShe">{{ item.title }}</h3>
              <div class="contact">
                <div class="mb-10">联系电话</div>
                <span>{{ item.phone || '--' }}</span>
              </div>
            </div>
          </div>
          <p class="intro">{{ html2Text(item.content) }}</p>
        </div>
      </div>
    </el-scrollbar>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100, 300]"
        background
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center>
      <DialogTitle title="专家信息" @close="dialogVisible = false" />
      <div class="p-20">
        <div class="specialist-content w-100% flex justify-center">
          <div class="avatar">
            <Image v-if="specialist.coverImg" class="avatar-img" :src="specialist.coverImg" alt="专家头像" />
            <div v-else class="default-avatar-img">
              <img src="@/assets/knowledgeStore/<EMAIL>" alt="" />
            </div>
          </div>
          <div>
            <h3 class="name">{{ specialist.title }}</h3>
            <div class="contact">
              <div class="mb-10">联系电话</div>
              <span>{{ specialist.phone || '--' }}</span>
            </div>
          </div>
        </div>
        <div class="content-box">
          <el-scrollbar>
            <MdPreview :modelValue="specialist.content" />
          </el-scrollbar>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.content-box {
  height: calc(100vh - 550px);
}

:deep(.md-editor) {
  background: transparent;

  p {
    color: #ecfff8 !important;
    // text-indent: 2em; // 添加首行缩进
  }
}

:deep(.el-dialog__headerbtn) {
  top: 38px;
  right: 5px;
}

.specialist-list {
  height: calc(100vh - 170px);
  padding: 0 15px 20px;
  border-radius: 10px;

  .specialist-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 5px;
    padding-bottom: 5px;
  }

  .specialist-card {
    width: 368px;
    height: 416px;
    padding: 20px 10px;
    margin-left: 5px;
    overflow: hidden;
    cursor: pointer;
    background: url(@/assets/knowledgeStore/zj_bk.webp) no-repeat;
    background-size: 100% 100%;
    border-radius: 12px;

    .intro {
      display: -webkit-box;
      width: 100%;
      padding: 0 20px;
      overflow: hidden;
      -webkit-line-clamp: 7;
      font-size: 14px;
      line-height: 1.6;
      color: #ecfff8;
      text-align: left;
      word-break: break-all;
      -webkit-box-orient: vertical;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    // float: right;
    margin-top: 10px;
    text-align: center;
  }
}

.specialist-content {
  display: flex;
  padding-bottom: 25px;
  margin-bottom: 24px;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 192px;
  height: 162px;
  background: url(@/assets/knowledgeStore/<EMAIL>) no-repeat;
  background-size: 100% 100%;

  .avatar-img {
    width: 111px;
    height: 111px;
    object-fit: cover;
    border-radius: 50%;
  }

  .default-avatar-img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 111px;
    height: 111px;
    border-radius: 60px;

    > img {
      width: 58px;
      height: 66px;
    }
  }
}

.name {
  width: 131px;
  height: 30px;
  font-size: 24px;
  font-weight: 400;
  line-height: 25px;
  color: #6dffd9;
  background: linear-gradient(0deg, #9fffcf 0.537109375%, #fff 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.contact {
  margin-top: 20px;
  margin-bottom: 16px;
  font-size: 15px;
  color: #ecfff8;
}
</style>
