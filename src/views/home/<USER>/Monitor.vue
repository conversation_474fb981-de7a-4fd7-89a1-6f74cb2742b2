<script setup lang="ts">
import type { DeviceOption } from '@/api/common/type'
import { storeToRefs } from 'pinia'
import { CommonAPI } from '@/api/common/common.ts'
import { MonitorAPI } from '@/api/monitor/monitor.ts'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'
import Bottom from './components/Bottom.vue'

const appStore = useAppStore()
const { farmlandId, deviceId } = storeToRefs(appStore)
deviceId.value = null
/**
 * 通过select切换地块
 */
function changeFarmlandId(id: number) {
  emitter.emit('selectFarmlandById', id)
}

/**
 * 通过select切换设备（墒情2）
 */
const deviceOption = ref<DeviceOption[]>([])
CommonAPI.deviceOption({ deviceType: 2 }).then((res) => {
  deviceOption.value = res
})

const loading = ref(false)
const monitorList = ref([])
const leftList = ref([])
const rightList = ref([])

/* 监控列表获取 */
function getList() {
  loading.value = true
  const params = {
    farmlandId: appStore.farmlandId,
    pageNum: -1,
    pageSize: -1,
  }
  MonitorAPI.monitorList(params)
    .then((res) => {
      monitorList.value = res?.records ?? []
      leftList.value = res?.records?.filter((_, index) => index % 2 === 0) ?? []
      rightList.value = res?.records?.filter((_, index) => index % 2 === 1) ?? []
    })
    .finally(() => {
      loading.value = false
    })
}

getList()

const monitorDialog = ref(false)
const formData = ref<any>({})
const channelld = ref('')
function openLive(item) {
  formData.value = { ...item }
  monitorDialog.value = true
  channelld.value = item?.channelId
}

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  // handleQuery()
  getList()
})
</script>

<template>
  <div class="flex flex-col">
    <PageLayout class="grow overflow-hidden">
      <template #left>
        <div v-loading="loading" class="empty hScroll w-full pb-10 pt-15">
          <AutoScroll v-if="leftList?.length > 0" :list="leftList" class="w-110% hScroll">
            <div v-for="(item, index) in leftList" :key="index" class="monitorBox">
              <div class="monitorTop">
                <div class="flex justify-center items-center h-full relative">
                  <Image v-if="item.snapUrl" :src="item.snapUrl" class="w-100% h-100% !absolute top-0 left-0 " />
                  <img src="@/assets/device/icon_bofang.png" class="w-44 h-44 relative z-10" alt="" @click="openLive(item)" />
                </div>
              </div>
              <div class="flex items-center">
                <img src="@/assets/device/icon_jkmc.png" class="w-13 h-16" alt="" />
                <span class="ml-12 monitorName">{{ item.channelName || '--' }}</span>
              </div>
            </div>
          </AutoScroll>
        </div>
      </template>
      <template #left-control>
        <div class="flex">
          <Select v-model="farmlandId" clearable :options="appStore.farmlandList" @change="changeFarmlandId" />
        </div>
      </template>
      <template #right>
        <div class="empty hScroll w-full pb-10 pt-15">
          <AutoScroll v-if="rightList?.length > 0" :list="rightList" class="w-110% hScroll">
            <div v-for="(item, index) in rightList" :key="index" class="monitorBox">
              <div class="monitorTop">
                <div class="flex justify-center items-center h-full relative">
                  <Image v-if="item.snapUrl" :src="item.snapUrl" class="w-100% h-100% !absolute top-0 left-0 " />
                  <img src="@/assets/device/icon_bofang.png" class="w-44 h-44 relative z-10" alt="" @click="openLive(item)" />
                </div>
              </div>
              <div class="flex items-center">
                <img src="@/assets/device/icon_jkmc.png" class="w-13 h-16" alt="" />
                <span class="ml-12 monitorName">{{ item.channelName || '--' }}</span>
              </div>
            </div>
          </AutoScroll>
        </div>
      </template>
    </PageLayout>
    <Bottom :monitorLoading="loading" :monitorList="monitorList" class="bounceInUp h-31vh shrink-0" />
    <PlayerDialog v-if="monitorDialog" v-model="monitorDialog" :deviceId="formData?.deviceId" :channelId="formData?.channelId" />
  </div>
</template>

<style lang="scss" scoped>
.hScroll {
  height: calc(69vh - 180px);
}

.monitorBox {
  box-sizing: border-box;
  width: 378px;
  height: 227px;
  padding: 11px;
  margin-bottom: 20px;
  margin-left: 40px;
  background: url(@/assets/device/jk_bk.png) center / 100% 100% no-repeat;

  .monitorTop {
    width: 100%;
    height: 85%;
    margin-bottom: 15px;
  }

  .monitorName {
    font-size: 14px;
    font-weight: 500;
    color: #ceffe6;
    background: linear-gradient(360deg, #00d851 0%, #fff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
