<script setup lang="ts">
import type { DeviceParam, OperationType, Planting, PlantingLog, PlantingLogRequest } from '@/api/farmland/type'
import { storeToRefs } from 'pinia'
import { FarmlandAPI } from '@/api/farmland/farmland.ts'
import { getState } from '@/hooks'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'
import Bottom from './components/Bottom.vue'
import Left from './components/Left.vue'
import Right1 from './components/Right1.vue'
import Right2 from './components/Right2.vue'

const appStore = useAppStore()
const { farmlandId } = storeToRefs(appStore)

/**
 * 通过select切换地块
 */
function changeFarmlandId(id: number) {
  emitter.emit('selectFarmlandById', id)
}

/** S 种植日志弹窗 */
function detail(row: Planting) {
  const { findDate, id, name } = row
  const operationTypeId = operationList.value.find(item => item.name === name[0])?.id
  queryParams.value = { plantingId: id, findDate, operationTypeId }
  tableList.value = []
  dialogVisible.value = true
  handleQuery()
}

const state = getState<PlantingLog>()
const { loading, dialogVisible, tableList } = toRefs(state)
const queryParams = ref<PlantingLogRequest>({})
function handleQuery() {
  loading.value = true
  FarmlandAPI.plantingLog(queryParams.value)
    .then((res) => {
      tableList.value = res
    })
    .finally(() => {
      loading.value = false
    })
}
/** E 种植日志弹窗 */

/** S 获取操作类型列表 */
const operationList = ref<OperationType[]>([])
function queryOperationList() {
  FarmlandAPI.operationTypeList().then((res) => {
    operationList.value = res || []
  })
}

queryOperationList()
/** E 获取操作类型列表 */

/** S 获取气象墒情数据 */
const soilList = ref<DeviceParam[]>([])
const soilLoading = ref(false)
function querySoil() {
  soilLoading.value = true
  FarmlandAPI.deviceParam(appStore.farmlandId)
    .then((res) => {
      soilList.value = res
    })
    .finally(() => {
      soilLoading.value = false
    })
}

querySoil()
/** E 获取气象墒情数据 */

/** S 获取病虫监测数据 */
const insectCountList = ref<DeviceParam[]>([])
const insectCountLoading = ref(false)
function queryInsectCount() {
  insectCountLoading.value = true
  FarmlandAPI.insectCount(appStore.farmlandId)
    .then((res) => {
      insectCountList.value = res
    })
    .finally(() => {
      insectCountLoading.value = false
    })
}

queryInsectCount()
/** E 获取病虫监测数据 */

watch(() => appStore.farmlandId, () => {
  querySoil()
  queryInsectCount()
})
</script>

<template>
  <div class="flex flex-col">
    <PageLayout class="grow overflow-hidden">
      <template #left>
        <Left @detail="detail" />
      </template>
      <template #left-control>
        <Select v-model="farmlandId" :options="appStore.farmlandList" clearable @change="changeFarmlandId" />
      </template>
      <template #right>
        <div class="flex flex-col h-full">
          <Right1 class="grow overflow-hidden" :soilList="soilList" :loading="soilLoading" />
          <Right2 class="h-190 shrink-0" :insectCountList="insectCountList" :loading="insectCountLoading" />
        </div>
      </template>
    </PageLayout>
    <Bottom v-loading="soilLoading || insectCountLoading" class="bounceInUp h-31vh shrink-0" :soilList="soilList" :insectCountList="insectCountList" @detail="detail" />

    <!--    地块详情弹窗 -->
    <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="plantDialog">
      <DialogTitle title="种植日志" class="mt-10" @close="dialogVisible = false" />
      <header class="flex gap-6 mb-20 mt-28">
        <el-input v-model="queryParams.handlerUser" placeholder="操作人员" class="!w-150" @change="handleQuery" />
        <el-select v-model="queryParams.operationTypeId" class="!w-150" placeholder="操作类型" clearable @change="handleQuery">
          <el-option v-for="item in operationList" :key="item.id" :value="item.id" :label="item.name" />
        </el-select>
        <el-date-picker v-model="queryParams.findDate" class="!w-180" type="date" value-format="YYYY-MM-DD" placeholder="选择日期" @change="handleQuery" />
      </header>

      <el-scrollbar v-loading="loading" height="27vw" class="!h-fit -ml-20">
        <div v-if="tableList.length" class="time-line flex flex-col gap-y-15 pl-43">
          <div v-for="item in tableList" :key="item.id">
            <header class="flex items-center flex-wrap -ml-23 mb-8 leading-22" text="15 #B6DFD2">
              <div class="w-14 h-14 rounded-1/2 bg-#13281a z-1" b="2 solid #579174" />
              <span class="ml-15">{{ item.logTime }}</span>
              <span class="ml-20 truncate">{{ item.personName }}</span>
              <span class="ml-20">作业明细：{{ item.content || '无' }}</span>
            </header>
            <footer class="relative w-255 h-163">
              <Image
                v-if="item.imageList?.length"
                :src="item.imageList[0].url"
                :preview-src-list="item.imageList.map((item) => item.url)"
                class="w-full h-full rounded-4"
              />
              <div v-else class="w-full h-full flex flex-col justify-center items-center gap-y-13 rounded-4 select-none" b="1 solid #364D3C">
                <img src="@/assets/common/icon_zanwu.webp" alt="" class="w-61 h-50" draggable="false" />
                <span text="15 #647D6B">暂无图片</span>
              </div>
              <div class="status absolute top-8 right-8 px-7 py-4 pointer-events-none" text="11 white">{{ item.operationTypeName }}</div>
            </footer>
          </div>
        </div>
        <div v-else class="empty !h-27vw" />
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">

</style>

<style lang="scss">
.plantDialog.plantDialog {
  --el-dialog-width: 668px;

  height: 737px;
  padding: 35px;
  background: url(@/assets/common/bg_668.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}

.time-line {
  position: relative;

  &::before {
    position: absolute;
    top: 20px;
    bottom: 0;
    left: 27px;
    z-index: 0;
    content: '';
    border-left: 1px dashed #364d3c;
  }
}

.status {
  background: url(@/assets/home/<USER>/ 100% no-repeat;
}
</style>
