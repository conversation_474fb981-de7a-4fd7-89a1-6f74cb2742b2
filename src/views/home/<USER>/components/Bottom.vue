<script setup lang="ts">
import type { Camera, Snap } from '@/api/monitor/type'
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor/monitor.ts'
import { getState } from '@/hooks'

const { monitorList, monitorLoading } = defineProps<{ monitorList: Camera[], monitorLoading: boolean }>()

const state = getState<Snap>()
const { queryParams, tableList, loading } = toRefs(state)
function handleQuery() {
  queryParams.value.deviceId = monitorId.value
  loading.value = true
  MonitorAPI.snapList(queryParams.value).then((res) => {
    tableList.value = res.records || []
  }).finally(() => {
    loading.value = false
  })
}

/**
 * 切换监控
 */
const monitorId = ref()

watch(() => monitorList, () => {
  if (monitorList.length) {
    monitorId.value = monitorList[0].id
    handleQuery()
  }
  else {
    monitorId.value = null
    tableList.value = []
  }
})
</script>

<template>
  <Card title="苗情记录" class="relative pr-15 pb-10">
    <header class="absolute right-15 top-25 flex pointer-events-auto">
      <Select v-model="monitorId" :options="monitorList" :props="{ label: 'channelName', value: 'id' }" @change="handleQuery" />
    </header>
    <section v-loading="monitorLoading || loading" class="main empty h-full pointer-events-auto">
      <HorizontalScroll class="w-full gap-20 pt-10 bg-#00000033">
        <div v-for="item in tableList.slice(0, 10)" v-if="tableList.length" :key="item.id" class="pb-10 relative">
          <img src="@/assets/monitor/box.png" class="absolute w-300 h-190" alt="" />
          <footer class="mt-5 pr-15" text="right">{{ dayjs(item.snapTime).format('YYYY-MM-DD') }}</footer>
          <Image :src="item.snapImage.url" class="w-292 h-160 ml-4 mt-5" draggable="false" :preview-src-list="[item.snapImage.url]" />
        </div>
        <div v-else class="empty !h-200" />
      </HorizontalScroll>
    </section>
  </Card>
</template>

<style scoped lang="scss">
.main {
  background: linear-gradient(0deg, rgba(24, 31, 37, 0.5) 0%, rgba(36, 51, 50, 0.5) 100%);
}
:deep(.title) {
  pointer-events: auto;
}

:deep(.arrow-left) {
  left: 0px;
}

:deep(.arrow-right) {
  right: 0px;
}
</style>
