<script setup lang="ts">
import type { Camera, DeviceCameraSnapRecord, Snap } from '@/api/monitor/type'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { MonitorAPI } from '@/api/monitor/monitor.ts'
import { getState } from '@/hooks'
import { useRafInterval } from '@/hooks/useRafInterval'

const { monitorList, monitorLoading } = defineProps<{ monitorList: Camera[], monitorLoading: boolean }>()

const state = getState<Snap>()
const { queryParams, tableList, loading } = toRefs(state)
function handleQuery() {
  queryParams.value.deviceId = monitorId.value
  loading.value = true
  MonitorAPI.snapList(queryParams.value).then((res) => {
    tableList.value = res.records || []
  }).finally(() => {
    loading.value = false
  })
}

/**
 * 切换监控
 */
const monitorId = ref()

watch(() => monitorList, () => {
  if (monitorList.length) {
    monitorId.value = monitorList[0].id
    handleQuery()
  }
  else {
    monitorId.value = null
    tableList.value = []
  }
})

/**
 * 苗情回溯弹窗相关
 */
const retrospectDialogVisible = ref(false)
const dateRange = ref<string[]>([])
const recordQueryParams = ref({
  startDate: null as string,
  endDate: null as string,
  pageSize: 1000,
  deviceId: null as number,
})

// 打开苗情回溯弹窗
function openRetrospectDialog() {
  if (!monitorId.value) {
    ElMessage.warning('请先选择监控设备')
    return
  }

  // 设置默认日期范围为半年内
  const endDate = dayjs()
  const startDate = endDate.subtract(6, 'month')
  dateRange.value = [startDate.format('YYYY-MM-DD'), endDate.format('YYYY-MM-DD')]

  recordQueryParams.value.deviceId = monitorId.value
  retrospectDialogVisible.value = true
  recordTableList.value = []
  handleRecordQuery()
}

// 查询苗情记录
const recordTableList = ref<DeviceCameraSnapRecord[]>([])
const recordLoading = ref(false)
const currentImageIndex = ref(0)
function handleRecordQuery() {
  if (dateRange.value?.length) {
    recordQueryParams.value.startDate = dateRange.value[0]
    recordQueryParams.value.endDate = dateRange.value[1]
  }
  else {
    recordQueryParams.value.startDate = null
    recordQueryParams.value.endDate = null
  }

  recordLoading.value = true

  // 停止当前的自动播放
  stopAutoPlay()

  MonitorAPI.record(recordQueryParams.value)
    .then((res) => {
      recordTableList.value = res.records || []
      currentImageIndex.value = 0 // 默认显示第一条数据

      // 如果有多张图片，启动自动播放
      if (recordTableList.value.length > 1) {
        // 延迟启动，让用户先看到第一张图片
        setTimeout(() => {
          startAutoPlay()
        }, 1000)
      }
    })
    .finally(() => {
      recordLoading.value = false
    })
}

// 日期范围限制函数
const firstDate = ref<Date>()
function calendarChange(val: Date[]) {
  firstDate.value = val[1] ? null : val[0]
}

function disabledDate(date: Date) {
  const isAfterDate = date > new Date()
  if (!firstDate.value)
    return isAfterDate

  // 计算6个月的范围限制
  const sixMonthsBefore = dayjs(firstDate.value).subtract(6, 'month').toDate()
  const sixMonthsAfter = dayjs(firstDate.value).add(6, 'month').toDate()

  return isAfterDate || date < sixMonthsBefore || date > sixMonthsAfter
}

// 自动播放相关
const isAutoPlaying = ref(false)
const autoPlayTimer = ref<NodeJS.Timeout | null>(null)
const pauseTimer = ref<NodeJS.Timeout | null>(null)

// 开始自动播放
function startAutoPlay() {
  if (recordTableList.value.length <= 1)
    return

  isAutoPlaying.value = true
  autoPlayTimer.value = setInterval(() => {
    if (currentImageIndex.value >= recordTableList.value.length - 1) {
      currentImageIndex.value = 0
    }
    else {
      currentImageIndex.value++
    }
  }, 2000) // 每2秒切换一张
}

// 停止自动播放
function stopAutoPlay() {
  isAutoPlaying.value = false
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = null
  }
}

// 暂停自动播放并设置恢复定时器
function pauseAutoPlay() {
  stopAutoPlay()

  // 清除之前的恢复定时器
  if (pauseTimer.value) {
    clearTimeout(pauseTimer.value)
  }

  // 3秒后恢复自动播放
  pauseTimer.value = setTimeout(() => {
    if (recordTableList.value.length > 1) {
      startAutoPlay()
    }
  }, 3000)
}

// 滑块值改变时切换图片
function handleSliderChange(value: number) {
  currentImageIndex.value = value
  // 滑动时暂停自动播放
  pauseAutoPlay()
}

// 获取当前显示的图片数据
const currentImageData = computed(() => {
  return recordTableList.value[currentImageIndex.value] || null
})

// 生成滑块的标记点
const sliderMarks = computed(() => {
  const marks: Record<number, string> = {}
  for (const [index, item] of recordTableList.value.entries()) {
    if (item.snapTime) {
      marks[index] = dayjs(item.snapTime).format('MM-DD')
    }
  }

  return marks
})

// 弹窗关闭时清理定时器
function closeRetrospectDialog() {
  retrospectDialogVisible.value = false
  stopAutoPlay()
  if (pauseTimer.value) {
    clearTimeout(pauseTimer.value)
    pauseTimer.value = null
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoPlay()
  if (pauseTimer.value) {
    clearTimeout(pauseTimer.value)
  }
})
</script>

<template>
  <Card title="苗情记录" class="relative pr-15 pb-10">
    <header class="absolute right-15 top-25 flex items-center gap-10 pointer-events-auto">
      <button
        class="retrospect-btn px-15 h-28 tracking-1 rounded-4"
        text="12 #CEFFE6"
        @click="openRetrospectDialog"
      >
        苗情回溯
      </button>
      <Select v-model="monitorId" :options="monitorList" :props="{ label: 'channelName', value: 'id' }" @change="handleQuery" />
    </header>
    <section v-loading="monitorLoading || loading" class="main empty h-full pointer-events-auto">
      <HorizontalScroll class="w-full gap-20 pt-10 bg-#00000033">
        <div v-for="item in tableList" v-if="tableList.length" :key="item.id" class="pb-10 relative">
          <img src="@/assets/monitor/box.png" class="absolute w-300 h-190" alt="" />
          <footer class="mt-5 pr-15" text="right">{{ dayjs(item.snapTime).format('YYYY-MM-DD') }}</footer>
          <Image :src="item.snapImage.url" class="w-292 h-160 ml-4 mt-5" draggable="false" :preview-src-list="[item.snapImage.url]" />
        </div>
        <div v-else class="empty !h-200" />
      </HorizontalScroll>
    </section>
  </Card>

  <!-- 苗情回溯弹窗 -->
  <el-dialog v-model="retrospectDialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="retrospect-dialog">
    <template #header>
      <DialogTitle title="苗情回溯" @close="closeRetrospectDialog" />
    </template>

    <div class="pt-10 px-20">
      <!-- 日期范围选择器 -->
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        :disabled-date="disabledDate"
        class="!w-300"
        @calendar-change="calendarChange"
        @change="handleRecordQuery"
      />

      <!-- 图片显示区域 -->
      <div v-loading="recordLoading" class="min-h-600 flex items-center justify-center bg-#181f2533 rounded-8 my-20">
        <div v-if="currentImageData && currentImageData.snapImage" class="w-full text-center">
          <Image
            :src="currentImageData.snapImage.url"
            class="w-full h-600 rounded"
            :preview-src-list="[currentImageData.snapImage.url]"
          />
        </div>
        <div v-else-if="!recordLoading" class="select-none" text="14 #86A29D"> 暂无图片数据 </div>
      </div>

      <!-- 时间轴滑块 -->
      <footer v-if="recordTableList.length">
        <!-- 自动播放控制 -->
        <div class="flex items-center justify-center mb-15">
          <button
            class="auto-play-btn px-20 h-32 rounded border border-#86A29D"
            text="12 #86A29D"
            hover="text-#CEFFE6 border-#CEFFE6"
            @click="isAutoPlaying ? stopAutoPlay() : startAutoPlay()"
          >
            {{ isAutoPlaying ? '暂停播放' : '自动播放' }}
          </button>
        </div>

        <el-slider
          v-model="currentImageIndex"
          :min="0"
          :max="recordTableList.length - 1"
          :marks="sliderMarks"
          :show-tooltip="false"
          class="time-slider"
          @change="handleSliderChange"
        />
      </footer>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.main {
  background: linear-gradient(0deg, rgba(24, 31, 37, 0.5) 0%, rgba(36, 51, 50, 0.5) 100%);
}
:deep(.title) {
  pointer-events: auto;
}

:deep(.arrow-left) {
  left: 0px;
}

:deep(.arrow-right) {
  right: 0px;
}

// 苗情回溯按钮样式
.retrospect-btn {
  background: linear-gradient(90deg, rgba(19, 80, 47, 0.8) 0%, #13502f 100%);
  border: 1px solid #86a29d;
  transition: all 0.3s;

  &:hover {
    background: linear-gradient(90deg, #13502f 0%, rgba(19, 80, 47, 0.8) 100%);
  }
}

// 自动播放按钮样式
.auto-play-btn {
  background: rgba(19, 80, 47, 0.3);
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    background: rgba(19, 80, 47, 0.6);
  }
}

// 时间轴滑块样式
.time-slider {
  :deep(.el-slider__runway) {
    background-color: rgba(134, 162, 157, 0.3);
  }

  :deep(.el-slider__bar) {
    background-color: #0eb36b;
  }

  :deep(.el-slider__button) {
    border-color: #0eb36b;
  }

  :deep(.el-slider__marks-text) {
    color: #86a29d;
    font-size: 10px;
  }
}
</style>

<style lang="scss">
.retrospect-dialog {
  &.el-dialog {
    --el-dialog-width: 1180px;
    height: 902px;
    padding: 25px;
    background: url(@/assets/common/bg_1180.webp) center / 100% 100% no-repeat;
  }
}
</style>
