<script setup lang="ts">
import type { EChartsOption } from '#/index'
import type { Weather, WeatherParam } from '@/api/soil/type'
import dayjs from 'dayjs'
import { useAppStore } from '@/store/app.ts'

const { weatherList, location, weatherDataD } = defineProps<{ weatherList: WeatherParam[], location: string, weatherDataD?: Weather }>()
const appStore = useAppStore()

// /** S 墒情折线数据获取 */
const soilCode = ref('')
watch(() => weatherList, () => {
  // xData.value = weatherList.map(item => dayjs(item.date).format('YYYY-MM-DD'))
  const currentYear = dayjs().year()
  xData.value = weatherList.map(item => `${currentYear}-${dayjs(item.date).format('MM-DD')}`)
})

const location1 = ref('')
watch(() => location, () => {
  location1.value = location
})

/** S 天气折线图  */
const weatherData = ref()
watch(() => weatherDataD, () => {
  weatherData.value = weatherDataD
  handleData()
})

// queryWeather()
const minTempData = ref([])
const maxTempData = ref([])
const humidityData = ref([])
const xData = ref([])
const todayIndex = ref(7)
function handleData() {
  minTempData.value = []
  maxTempData.value = []
  xData.value = []
  todayIndex.value = 7
  for (const [index, item] of weatherData.value.points?.entries()) {
    minTempData.value.push(item.tempMin)
    maxTempData.value.push(item.tempMax)
    humidityData.value.push(item.humidity)
    xData.value.push(item.date)
    if (item.today) {
      todayIndex.value = index
    }
  }
}

/** E 病虫折线数据获取 */

const option = computed(() => {
  return {
    grid: {
      top: '20%',
      bottom: '5%',
      left: '1%',
      right: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter(params, ticket, callback) {
        let htmlStr = ''
        const valMap = {}
        for (const [i, param] of params.entries()) {
          const xName = param.name // x轴的名称
          const seriesName = param.seriesName // 图例名称
          const value = param.value // y轴值
          const color = param.color // 图例颜色

          // 过滤无效值
          if (value == '-') {
            continue
          }

          // 过滤重叠值
          if (valMap[seriesName] == value) {
            continue
          }

          if (i === 0) {
            htmlStr += `${xName}<br/>` // x轴的名称
          }

          htmlStr += '<div>'
          // 为了保证和原来的效果一样，这里自己实现了一个点的效果
          htmlStr
              += `<span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${
              color
            };"></span>`

          // 圆点后面显示的文本
          htmlStr += `${seriesName}：${value}`

          htmlStr += '</div>'
          valMap[seriesName] = value
        }

        return htmlStr
      },
    },
    dataZoom: [{ type: 'inside' }],
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        // symbol: ['none', 'arrow'],
        // symbolOffset: [0, 20],
        lineStyle: {
          color: 'rgba(55,255,191,0.2)',
        },
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: true,
    },
    yAxis: [
      {
        type: 'value',
        alignTicks: true,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
        },
        name: `单位：℃`,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        minInterval: 1,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
      {
        type: 'value',
        alignTicks: true,
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
        },
        minInterval: 1,
        name: `单位：%`,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
      {
        type: 'value',
        show: false,
      },
    ],
    legend: {
      show: true,
      top: '5%',
      right: '5%',
      icon: 'circle',
      itemHeight: 10,
      itemGap: 30,
      textStyle: {
        color: '#B6DFD2',
      },
    },
    series: [
      {
        name: '最高温度',
        type: 'line',
        yAxisIndex: 0,
        // symbol: 'emptyCircle',
        smooth: true,
        symbol: 'none',
        // symbolSize: 6,
        itemStyle: {
          color: '#2ED372',
        },
        lineStyle: {
          width: 1.5,
        },
        data: maxTempData.value.slice(0, todayIndex.value + 1),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,211,114,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(46,211,114,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '最高温度',
        type: 'line',
        yAxisIndex: 0,
        // symbol: 'emptyCircle',
        smooth: true,
        symbol: 'none',
        // symbolSize: 6,
        itemStyle: {
          color: '#2ED372',
        },
        lineStyle: {
          width: 1.5,
          type: [4, 2],
        },
        data: maxTempData.value.map((item, index) => (index < todayIndex.value ? '-' : item)),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,211,114,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(46,211,114,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '最低温度',
        type: 'line',
        yAxisIndex: 0,
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        itemStyle: {
          color: '#2E9BF3',
        },
        lineStyle: {
          width: 1.5,
        },
        data: minTempData.value.slice(0, todayIndex.value + 1),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,155,243,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(46,155,243,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '最低温度',
        type: 'line',
        yAxisIndex: 0,
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        itemStyle: {
          color: '#2E9BF3',
        },
        lineStyle: {
          width: 1.5,
          type: [4, 2],
        },
        data: minTempData.value.map((item, index) => (index < todayIndex.value ? '-' : item)),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,155,243,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(46,155,243,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '湿度',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        itemStyle: {
          color: '#FCC84F',
        },
        lineStyle: {
          width: 1.5,
        },
        data: humidityData.value.slice(0, todayIndex.value + 1),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(252,200,79,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(252,200,79,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '湿度',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        itemStyle: {
          color: '#FCC84F',
        },
        lineStyle: {
          width: 1.5,
          type: [4, 2],
        },
        data: humidityData.value.map((item, index) => (index < todayIndex.value ? '-' : item)),
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(252,200,79,0.2)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(252,200,79,0)', // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
      // 自定义渐变色标线
      {
        type: 'custom',
        name: '今日',
        silent: true,
        z: 9, // 确保在其他系列上方显示
        renderItem(params, api) {
          // 获取 x 轴位置
          const point = api.coord([todayIndex.value, 0])

          // 获取图表的高度
          const coordSys = params.coordSys
          const rectHeight = coordSys.height

          // 返回矩形
          return {
            type: 'rect',
            shape: {
              x: point[0] - 2.5, // 中心点减去宽度的一半
              y: coordSys.y,
              width: 5,
              height: rectHeight,
            },
            style: {
              fill: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#C07052', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(192,112,82,0.1)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          }
        },
        itemStyle: {
          color: '#C07052',
        },
        data: [xData.value[todayIndex.value]], // 只需要传递当前日期，实际不会使用
      },
    ],
  } as EChartsOption
})
</script>

<template>
  <Card
    title="天气趋势图"
    class="relative pr-15 pb-10"
  >
    <div class="!w-448 title-l flex justify-end items-center absolute left-0 top-0 z-10 pr-35 -my-10">
      <div class="!w-210 text-right overflow-hidden whitespace-nowrap break-all text-ellipsis">
        <img src="@/assets/common/icon_dingwei.png" class="w-13 h-14 mr-5" alt="天气趋势图" />
        <span class="text-300 !text-15 text-#CEFFE6 ">{{ location1 }}</span>
      </div>
    </div>

    <section class="main empty h-full pointer-events-auto">
      <Chart v-if="xData.length" :option="option" />
    </section>
  </Card>
</template>

<style scoped lang="scss">
.main {
  background: linear-gradient(0deg, rgba(24, 31, 37, 0.5) 0%, rgba(36, 51, 50, 0.5) 100%);
}

:deep(.title) {
  pointer-events: auto;
}

.title-l {
  height: var(--card-title-height);
}
</style>
