<script setup lang="ts">
import type { ChartData, EChartsOption } from '#/index'
import type { DeviceParam, Planting } from '@/api/farmland/type'
import dayjs from 'dayjs'
import { FarmlandAPI } from '@/api/farmland/farmland.ts'
import { useOn } from '@/hooks/useOn.ts'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'

const { soilList, insectCountList } = defineProps<{ soilList: DeviceParam[], insectCountList: DeviceParam[] }>()
const emit = defineEmits<{
  (e: 'detail', data: Planting): void
}>()
const appStore = useAppStore()
const ratio = computed(() => appStore.ratio)

/**
 * 更换地块后重置数据
 */
watch(() => appStore.farmlandId, () => {
  soilCode.value = null
  insectCountCode.value = null
  soilTrend.value = []
  insectCountTrend.value = []
  planting.value = null
  plantingLogList.value = []
}, { flush: 'pre' })

/**
 * 点击种植情况
 */
const planting = ref<Planting>({})
const plantingLogList = ref<{ logTime: string, operationTypeName: string }[]>([])
const plantingLogLoading = ref(false)
useOn(emitter, 'cropClick', (data: Planting) => {
  if (!data) {
    soilTrend.value = []
    insectCountTrend.value = []
    planting.value = null
    plantingLogList.value = []
    return
  }

  planting.value = data
  plantingLogLoading.value = true
  FarmlandAPI.plantingLog({ plantingId: data.id })
    .then((res) => {
      // 处理数据，将同一天的operationTypeName合并
      const map: Record<string, string> = {}
      for (const item of res) {
        if (map[item.logTime]) {
          map[item.logTime] += `,${item.operationTypeName}`
        }
        else {
          map[item.logTime] = item.operationTypeName
        }
      }

      plantingLogList.value = Object.entries(map).map(([logTime, operationTypeName]) => ({ logTime, operationTypeName }))
    })
    .finally(() => {
      plantingLogLoading.value = false
    })
})

/** S 墒情折线数据获取 */
const soilCode = ref('')
watch(() => soilList, () => {
  soilCode.value = soilList[0]?.paramCode
})
const soil = computed(() => {
  const item = soilList.find(item => item.paramCode === soilCode.value)
  return item || {}
})
const soilTrend = ref<ChartData[]>([])
const soilTrendLoading = ref(false)
watch(() => [soilCode.value, planting.value, appStore.farmlandId], () => {
  if (!soilCode.value || !planting.value)
    return
  soilTrendLoading.value = true
  FarmlandAPI.soilTrend({
    paramCode: soilCode.value,
    farmlandId: appStore.farmlandId,
    startDate: planting.value.plantingDate,
    endDate: planting.value.harvestDate || dayjs().format('YYYY-MM-DD'),
  })
    .then((res) => {
      soilTrend.value = res || []
    })
    .finally(() => {
      soilTrendLoading.value = false
    })
})
/** E 墒情折线数据获取 */

/** S 病虫折线数据获取 */
const insectCountCode = ref('')
watch(() => insectCountList, () => {
  insectCountCode.value = insectCountList[0]?.paramCode
})
const insectCount = computed(() => {
  const item = insectCountList.find(item => item.paramCode === insectCountCode.value)
  return item || {}
})
const insectCountTrend = ref<ChartData[]>([])
const insectCountTrendLoading = ref(false)
watch(() => [insectCountCode.value, planting.value, appStore.farmlandId], () => {
  if (!insectCountCode.value || !planting.value)
    return
  insectCountTrendLoading.value = true
  FarmlandAPI.insectCountTrend({
    paramCode: insectCountCode.value,
    farmlandId: appStore.farmlandId,
    startDate: planting.value.plantingDate,
    endDate: planting.value.harvestDate || dayjs().format('YYYY-MM-DD'),
  })
    .then((res) => {
      insectCountTrend.value = res || []
    })
    .finally(() => {
      insectCountTrendLoading.value = false
    })
})
/** E 病虫折线数据获取 */

const xData = computed(() => {
  const x1 = soilTrend.value.map(item => dayjs(item.x).format('YYYY-MM-DD'))
  const x2 = insectCountTrend.value.map(item => dayjs(item.x).format('YYYY-MM-DD'))
  const x3 = plantingLogList.value.map(item => item.logTime)
  return [...new Set([...x1, ...x2, ...x3])].sort((a, b) => dayjs(a).diff(dayjs(b)))
})
const option = computed(() => {
  return {

    grid: {
      top: '20%',
      bottom: '15%',
      left: 70 * ratio.value,
      right: 70 * ratio.value,
      containLabel: false,
    },
    tooltip: {
      trigger: 'axis',
    },
    dataZoom: [{ type: 'inside' }],
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        // symbol: ['none', 'arrow'],
        // symbolOffset: [0, 20],
        lineStyle: {
          color: 'rgba(55,255,191,0.2)',
        },
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: true,
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
        },
        name: soil.value.unit && `单位:${soil.value.unit}`,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        minInterval: 1,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
        },
        minInterval: 1,
        name: insectCount.value.unit && `单位:${insectCount.value.unit}`,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
      {
        type: 'value',
        show: false,
      },
    ],
    legend: {
      show: true,
      top: '5%',
      right: 100 * ratio.value,
      icon: 'circle',
      itemHeight: 10,
      itemGap: 30,
      textStyle: {
        color: '#B6DFD2',
      },
    },
    series: [
      {
        name: soil.value.paramName,
        data: soilTrend.value.map(item => [dayjs(item.x).format('YYYY-MM-DD'), item.y]),
        type: 'line',
        smooth: true,
        symbol: 'none',
        color: '#2ED372',
        yAxisIndex: 0,
        areaStyle: {
          color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: 'rgba(46,211,114,0.3)' }, { offset: 1, color: 'rgba(46,211,114,0)' }] },
        },
      },
      {
        name: insectCount.value.paramName,
        data: insectCountTrend.value.map(item => [dayjs(item.x).format('YYYY-MM-DD'), item.y]),
        type: 'line',
        smooth: true,
        symbol: 'none',
        color: '#0194F1',
        yAxisIndex: 1,
        areaStyle: {
          color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: 'rgba(1,148,241,0.3)' }, { offset: 1, color: 'rgba(1,148,241,0)' }] },
        },
      },
      /**
       * 标绘种植日志
       */
      {
        type: 'bar',
        barWidth: 3,
        z: 1,
        tooltip: {
          show: false,
        },
        yAxisIndex: 2,
        data: plantingLogList.value.map(item => ({ name: item.operationTypeName, value: [item.logTime, 1] })),
        itemStyle: {
          color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: '#40F886' }, { offset: 1, color: 'rgba(64,248,134,0.05)' }] },
        },
        markPoint: {
          symbol: 'path://M5,0 L55,0 Q60,0 60,5 L60,18 Q60,23 55,23 L35,23 L30,30 L25,23 L5,23 Q0,23 0,18 L0,5 Q0,0 5,0 Z',
          symbolSize(params: any) {
            if (params) {
              // 根据文本长度动态计算大小（减小高度）
              const textWidth = Math.max(45, params.name.length * 10 + 20)
              return [textWidth, 26]
            }
            else {
              return [50, 26]
            }
          },
          animation: true,
          symbolOffset: [0, 0],
          silent: false,
          itemStyle: {
            color: 'rgba(24, 31, 37, 0.9)',
            borderColor: '#40FA86',
            borderWidth: 1.5,
          },
          label: {
            show: true,
            formatter: '{b}',
            color: '#CEFFE6',
            fontSize: 11,
            offset: [0, -4],
            verticalAlign: 'middle',
          },
          data: plantingLogList.value.map(item => ({
            name: item.operationTypeName,
            coord: [item.logTime, 1],
            yAxisIndex: 2,
          })),
        },
      },
    ],
  } as EChartsOption
})

// 图表引用
const chartRef = useTemplateRef('chartRef')

/**
 * 点击markPoint打开种植日志弹窗
 */
watch(() => option.value, () => {
  nextTick(() => {
    const chart = chartRef.value?.getChart()
    if (chart) {
      // 先移除之前的事件监听器
      chart.off('click')
      // 添加点击事件监听器
      chart.on('click', (params: any) => {
        console.log('params', params, '1', plantingLogList.value)
        // 只处理 markPoint 的点击事件
        if (params.componentType === 'markPoint') {
          const findDate = params.data.coord[0]
          emit('detail', { ...planting.value, name: params.name.split(','), findDate })
        }
      })
    }
  })
}, { flush: 'post' })
</script>

<template>
  <Card :title="`${planting?.name || ''}种植日志`" class="relative pr-15 pb-10">
    <header class="absolute right-15 top-25 flex pointer-events-auto">
      <Select v-model="soilCode" :options="soilList" :props="{ label: 'paramName', value: 'paramCode' }" />
      <Select v-model="insectCountCode" :options="insectCountList" :props="{ label: 'paramName', value: 'paramCode' }" />
    </header>
    <section v-loading="soilTrendLoading || insectCountTrendLoading || plantingLogLoading" class="main empty h-full pointer-events-auto">
      <Chart v-if="xData.length" ref="chartRef" :option="option" />
    </section>
  </Card>
</template>

<style scoped lang="scss">
.main {
  background: linear-gradient(0deg, rgba(24, 31, 37, 0.5) 0%, rgba(36, 51, 50, 0.5) 100%);
}

:deep(.title) {
  pointer-events: auto;
}
</style>
