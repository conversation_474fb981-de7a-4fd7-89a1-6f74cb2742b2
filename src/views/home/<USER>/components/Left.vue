<script setup lang="ts">
import type { Planting } from '@/api/farmland/type'
import { FarmlandAPI } from '@/api/farmland/farmland.ts'
import { getState } from '@/hooks'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'

const emit = defineEmits<{
  (e: 'detail', data: Planting): void
}>()
const state = getState<Planting>()
const { loading, tableList, formData } = toRefs(state)
const typeList = [
  { id: 1, title: '当期', value: false },
  { id: 2, title: '历史', value: true },
]
const type = ref(false)
function toggle(raw: typeof typeList[0]) {
  if (type.value === raw.value)
    return
  type.value = raw.value
  handleQuery()
}

const appStore = useAppStore()
function handleQuery() {
  loading.value = true
  FarmlandAPI.planting({ farmlandId: appStore.farmlandId, status: type.value })
    .then((res) => {
      tableList.value = res
      if (res.length) {
        formData.value = res[0]
        emitter.emit('cropClick', res[0])
      }
      else {
        emitter.emit('cropClick', null)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

handleQuery()

function detail(row: typeof state.formData) {
  emit('detail', row)
}

function detail2(row: typeof state.formData) {
  formData.value = row
  emitter.emit('cropClick', row)
}

watch(() => appStore.farmlandId, () => {
  handleQuery()
})
</script>

<template>
  <Card title="种植情况" class="h-full">
    <header class="flex justify-end">
      <button
        v-for="item in typeList" :key="item.id"
        class="button use-bg w-61 h-39 leading-23 tracking-2"
        :class="{ active: type === item.value }"
        text="12 #86A29D"
        @click="toggle(item)"
      >
        {{ item.title }}
      </button>
    </header>
    <section v-loading="loading" class="h-scroll">
      <el-scrollbar v-if="tableList.length" class="bg-#004831/25" height="100%">
        <div
          v-for="item in tableList" :key="item.id"
          class="item px-26 pb-21 cursor-pointer" b="1 solid transparent"
          :class="{ active: item.id === formData.id }"
          @click="detail2(item)"
        >
          <header class="flex items-center py-14" b="b-1 dashed #37FFBF/20">
            <div class="relative w-19 h-18">
              <img src="@/assets/home/<USER>" alt="" class="absolute w-67 w-66 left-1/2 top-1/2 -translate-1/2" />
            </div>
            <div class="ml-12">
              <span class="title">{{ item.name }}</span>
            </div>
            <button class="ml-auto tracking-2" text="12 #86A29D" @click.stop="detail(item)"> 查看> </button>
          </header>
          <section class="flex gap-x-20 mt-16">
            <aside class="imgContainer use-bg shrink-0 w-138 h-139 p-8">
              <Image v-if="item.imageList?.length" :src="item.imageList[0].url" :preview-src-list="item.imageList.map(item => item.url)" class="w-full h-full" />
              <div v-else class="w-full h-full flex flex-col justify-center items-center gap-y-13 rounded-4 select-none">
                <img src="@/assets/common/icon_zanwu.webp" alt="" class="w-61 h-50" draggable="false" />
                <span text="12 #647D6B">暂无图片</span>
              </div>
            </aside>
            <aside class="flex flex-col justify-between overflow-hidden py-12">
              <div class="labelValue truncate">
                <span class="label">种植人员：</span>
                <span class="value">{{ item.plantingUserName }}</span>
              </div>
              <div class="labelValue truncate">
                <span class="label">种植日期：</span>
                <span class="value">{{ item.plantingDate }}</span>
              </div>
              <div class="labelValue truncate">
                <span class="label">生长周期：</span>
                <span class="value">{{ item.growthCycleName }}</span>
              </div>
              <div class="labelValue truncate">
                <span class="label">采收日期：</span>
                <span class="value">{{ item.harvestDate ?? item.expectHarvestDate }}</span>
              </div>
            </aside>
          </section>
        </div>
      </el-scrollbar>
      <div v-else class="empty h-full" />
    </section>
  </Card>
</template>

<style scoped lang="scss">
.button {
  transition: 0.3s all;

  &.active {
    color: #ceffe6;
    background-image: url(@/assets/common/radio-bg.webp);
  }
}

.title {
  color: #ceffe6;
  background: linear-gradient(0deg, #00d851 0%, #fff 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.imgContainer {
  background-image: url(@/assets/home/<USER>
}

.item {
  transition: 0.3s all;

  &.active {
    border-color: #62e08b;
    box-shadow: 0 0 17px 3px rgba(44, 255, 123, 0.4) inset;
  }
}

.h-scroll {
  height: calc(100% - 1.5vw);
}

.labelValue {
  font-size: 14px;
  color: #b6dfd2;

  .value {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}
</style>
