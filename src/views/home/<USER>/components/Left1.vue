<script setup lang="ts">
import type { EChartsOption } from '#/index'
import { ref } from 'vue'
import { InsectAPI } from '@/api/insect/insect.ts'
import { useAppStore } from '@/store/app.ts'

// onMounted(() => {
//   setTimeout(() => {
//     handleQuery()
//   }, 100)
// })
// const emit = defineEmits<{
//   (e: 'detail', data: Planting): void
// }>()
const typeList = [
  { id: 0, title: '本日', value: 0, key: 'day' },
  { id: 1, title: '本周', value: 1, key: 'week' },
  { id: 2, title: '本月', value: 2, key: 'month' },
  { id: 3, title: '本年', value: 3, key: 'year' },
]
// const state = getState<Planting>()
// const { loading, tableList, formData } = toRefs(state)
const type = ref(0)
function toggle(raw: typeof typeList[0]) {
  if (type.value === raw.value)
    return
  type.value = raw.value
  handleQuery()
}

//

const appStore = useAppStore()
const dataX = ref<string[]>([])
const dataY = ref([])
const loading = ref(false)
function handleQuery() {
  loading.value = true
  InsectAPI.insectCount(typeList[type.value].key, {
    farmlandId: appStore.farmlandId,
    deviceId: appStore.deviceId,
  }).then((res) => {
    dataX.value = []
    dataY.value = []
    for (const item of res.values) {
      dataX.value.push(item.x + (res.xunit || ''))
      dataY.value.push(item.y)
    }
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

const option = computed(() => {
  return {
    grid: {
      top: '15%',
      bottom: 0,
      left: '8%',
      right: '12%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        for (const [index, param] of params.entries()) {
          result += `
          <div class="flex justify-between">
            <div>
              <span class="inline-block w-10Px h-10Px rounded-10Px mr-5Px bg-#3DFCCB"></span>
              <span>${param.seriesName}</span>
            </div>
            <span>${param.value}</span>
          </div>`
        }

        return result
      },
    },
    xAxis: {
      type: 'category',
      // data: computed(() => {
      //   if (type.value === 0) {
      //     return dataX.value.map((item) => {
      //       const parts = item.split(' ');
      //       return parts.length > 1 ? `${parts[0]}\n${parts[1]}` : item;
      //     });
      //   }
      //   return dataX.value;
      // }).value,
      data: dataX.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: type.value === 0,
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
      },
      name: '单位:只',
      minInterval: 1,
      nameLocation: 'end',
      nameRotate: 0,
      nameTextStyle: {
        color: '#789590',
        lineHeight: -5,
        verticalAlign: 'bottom',
        fontSize: 9,
      },
      z: 10,
      zlevel: 10,
    },
    // legend: {
    //   show: true,
    //   right: 0,
    //   icon: 'roundRect',
    //   itemWidth: 10,
    //   itemHeight: 6,
    //   itemGap: 30,
    // },
    dataZoom: [
      {
        type: 'inside',
      },
    ],
    series: [
      {
        name: '病虫数量',
        data: dataY.value ?? [],
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 7,
        symbolOffset: [0, 0],
        symbolKeepAspect: false,
        symbolRotate: null,
        color: '#3DFCCB',
        itemStyle: {
          // 将颜色改为带有透明度的 rgba 格式，这里设置透明度为 0.5
          shadowColor: 'rgba(61, 252, 203, 1)',
          // 增大阴影范围
          shadowBlur: 6,
          // 将颜色改为带有透明度的 rgba 格式，这里设置透明度为 0.5
          color: 'rgba(37, 37, 37, 0.9)',
          borderColor: 'rgba(61, 252, 203, 1)',
          borderWidth: 2,
        },
        lineStyle: {
          color: '#3DFCCB',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,211,114,0.15)',
              },
              {
                offset: 1,
                color: 'rgba(46,211,114,0)',
              },
            ],
          },
        },
      },
    ],
  } as EChartsOption
})

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card title="病虫数量" class="h-full">
    <header class="flex justify-end mt--15">
      <button
        v-for="item in typeList" :key="item.id"
        class="button use-bg w-61 h-39 leading-23 tracking-2"
        :class="{ active: type === item.value }"
        text="12 #86A29D"
        @click="toggle(item)"
      >
        {{ item.title }}
      </button>
    </header>

    <div v-loading="loading" class="hMani">
      <Chart v-if="dataX.length" :option="option" class="w-120% -ml-20" />
      <div v-else class="empty" />
    </div>
  </Card>
</template>

<style scoped lang="scss">
.button {
  transition: 0.3s all;

  &.active {
    color: #ceffe6;
    background-image: url(@/assets/common/radio-bg.webp);
  }
}

.hMani {
  height: var(--chart-height);
}
</style>
