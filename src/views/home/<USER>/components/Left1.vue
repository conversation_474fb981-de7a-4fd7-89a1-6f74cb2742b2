<script setup lang="ts">
import type { EChartsOption } from '#/index'
import { ref } from 'vue'
import { SoilAPI } from '@/api/soil/soil.ts'
import SelectYearRange from '@/components/SelectYearRange/SelectYearRange.vue'
import { useAppStore } from '@/store/app.ts'

// 年份范围
const currentYear = new Date().getFullYear().toString()
const previousYear = (new Date().getFullYear() - 1).toString()
const yearRange = ref([previousYear, currentYear])

const dataY = ref([])
const xData = ref<string[]>([])

const appStore = useAppStore()
const loading = ref(false)
// 温度对比
function handleQuery() {
  loading.value = true
  SoilAPI.temperature({
    years: yearRange.value?.length > 0 ? yearRange.value.join(',') : [],
    farmlandId: appStore.farmlandId,
    deviceId: appStore.deviceId,
  }).then((res) => {
    xData.value = []
    dataY.value = []
    for (const key in res) {
      const x = []
      if (Array.isArray(res[key])) {
        for (const item of res[key]) {
          if (item.hasOwnProperty('x')) {
            x.push(item.x)
            xData.value.push(item.x)
          }
        }

        dataY.value.push({ name: key, data: res[key], x })
      }
    }

    // 去重并排序
    xData.value = [...new Set(xData.value)]
  }).finally(() => {
    loading.value = false
  })
}

// 年份选择
function handleYearChange(val: string[]) {
  if (val.length > 0) {
    yearRange.value = val
    handleQuery()
  }
}

const option = computed(() => {
  return {
    grid: {
      top: '13%',
      bottom: 0,
      left: 0,
      right: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
      },
      name: '单位:℃',
      nameLocation: 'end',
      nameRotate: 0,
      nameTextStyle: {
        color: '#789590',
        lineHeight: -8,
        verticalAlign: 'bottom',
        fontSize: 9,
      },
      z: 10,
      zlevel: 10,
    },
    // legend: {
    //   show: true,
    //   right: 0,
    //   icon: 'roundRect',
    //   itemWidth: 10,
    //   itemHeight: 6,
    //   itemGap: 30,
    // },
    dataZoom: [
      {
        type: 'inside',
      },
    ],
    series: [
      {
        name: dataY.value[0].name ?? '',
        data: (dataY.value[0]?.data?.length > 0) ? (dataY.value[0]?.data?.map(item => [item.x, item.y])) : [],
        type: 'line',
        smooth: true,
        symbol: 'none',
        color: '#2ED372',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(46,211,114,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(46,211,114,0)',
              },
            ],
          },
        },
      },
      {
        name: dataY.value[1].name ?? '',
        data: dataY.value[1]?.data?.length > 0 ? dataY.value[1]?.data?.map(item => [item.x, item.y]) : [],
        type: 'line',
        smooth: true,
        symbol: 'none',
        color: '#0194F1',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(1,148,241,0.3)',
              },
              {
                offset: 1,
                color: 'rgba(1,148,241,0)',
              },
            ],
          },
        },
      },
    ],
  } as EChartsOption
})

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card title="温度对比" class="h-full">
    <header class="flex justify-end mt--10">
      <SelectYearRange v-model="yearRange" @change="handleYearChange" />
    </header>
    <div v-loading="loading" class="hMani">
      <Chart v-if="xData.length" :option="option" class="chart" />
      <div v-else class="empty" />
    </div>
  </Card>
</template>

<style scoped lang="scss">
.hMani {
  height: var(--chart-height);
}
</style>
