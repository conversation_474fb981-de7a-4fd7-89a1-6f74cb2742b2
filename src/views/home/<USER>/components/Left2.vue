<script setup lang="ts">
import type { EChartsOption } from '#/index'
import type { PieVO } from '@/api/insect/type'
import { ref } from 'vue'
import { InsectAPI } from '@/api/insect/insect.ts'
import SelectYearRange from '@/components/SelectYearRange/SelectYearRange.vue'
import { useAppStore } from '@/store/app.ts'

// 年份范围
const currentYear = new Date().getFullYear().toString()
const previousYear = (new Date().getFullYear() - 1).toString()
const yearRange = ref([previousYear, currentYear])

const dataX = ref([])
const dataY = ref([])
const xData = ref<string[]>([])

// onMounted(() => {
//   setTimeout(() => {
//     handleQuery()
//   }, 100)
// })

const appStore = useAppStore()
const loading = ref(false)

function handleQuery() {
  loading.value = true
  InsectAPI.insectTypeCount(
    {
      years: yearRange.value?.length > 0 ? yearRange.value.join(',') : [],
      farmlandId: appStore.farmlandId,
      deviceId: appStore.deviceId,
    },
  ).then((res) => {
    pieChartData.value = res?.map((item) => {
      const { count, ...rest } = item
      return {
        ...rest,
        value: count,
      }
    })
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

// 年份选择
function handleYearChange(val: string[]) {
  if (val.length > 0) {
    yearRange.value = val
    handleQuery()
  }
}

/** S 饼图 */
const pieChartData = ref<PieVO[]>([])
const pieOption = computed(() => {
  return {
    legend: {
      type: 'scroll', // 启用滚动
      orient: 'horizontal', // 垂直布局
      // width: '200',
      right: '0',
      // y: 'center',
      pageIconColor: '#5470c6', // 滚动按钮颜色
      pageTextStyle: {
        color: '#707070', // 滚动页码文字颜色
      },
      pageButtonGap: 10, // 上下按钮与文字的间距
      bottom: '15%',
      left: '5%',
      icon: 'rect',
      itemWidth: 16,
      itemHeight: 8,
      itemGap: 13,
      textStyle: { // 图例文字的样式
        color: '#BDD0F7',
      },
    },
    grid: {
      top: '10%',
      bottom: '20%',
      left: '10%',
      right: '10%',
    },
    color: ['#FFCD34', '#0099F9', '#51C4B2', '#35D04F'],
    tooltip: {
      trigger: 'item',
      show: true,
    },
    title: {
      text: '病虫种类',
      left: '49%', // 精确设置水平居中
      top: '36%', // 精确设置垂直居中，与饼图中心位置一致
      textAlign: 'center',
      textStyle: {
        color: '#BDD0F7',
        fontSize: 14,
      },
      subtextStyle: {
        textAlign: 'center',
      },
    },
    series: [
      {
        name: '病虫种类',
        type: 'pie',
        radius: ['30%', '45%'],
        center: ['50%', '40%'],
        // roseType: 'area',
        // 饼图间隙，从echarts5.5.0开始支持
        // padAngle: 4,
        itemStyle: {
          borderRadius: 2,
        },
        data: pieChartData.value,
        label: {
          show: false,
          color: '#BDD0F7',
          formatter: '{c}次 {d}%',
          padding: [-15, -70],
        },
        labelLine: {
          show: false,
        },
        labelLayout: {
          verticalAlign: 'bottom',
          dy: -10,
          // moveOverlap: 'shiftY',
        },
      },
    ],
  } as EChartsOption
})
/** E 饼图 */

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card title="病虫种类" class="h-full">
    <header class="flex justify-end mt--10">
      <SelectYearRange v-model="yearRange" @change="handleYearChange" />
    </header>
    <div v-loading="loading" class="w-377 h-300">
      <Chart v-if="pieChartData.length" class="pieChart w-377 h-full -ml-30 -mr-60" :option="pieOption" />
      <div v-else class="empty" />
    </div>
  </Card>
</template>

<style scoped lang="scss">
.pieChart {
  position: relative;

  // &::before {
  //  position: absolute;
  //  top: 30%;
  //  left: 50%;
  //  z-index: 0;
  //  width: 230.4px;
  //  height: 230.6px;
  //  content: '';
  //  background: url(@/assets/map/<EMAIL>) center / 100% 100% no-repeat;
  //  transform: translate(-50%, -50%);
  // }
}
</style>
