<script setup lang="ts">
import type { EChartsOption } from '#/index'
import dayjs from 'dayjs'
import { ref } from 'vue'
import { InsectAPI } from '@/api/insect/insect.ts'
import { useAppStore } from '@/store/app.ts'

// 年份范围
const year = ref(dayjs().format('YYYY'))

const dataY = ref([])
const xData = ref<string[]>([])

const insectName = ref([])
const optionList = ref([])

// 选择种类
function changeSelete(val) {
  handleQuery()
}

const appStore = useAppStore()
const loading = ref(false)
function handleQuery() {
  loading.value = true
  xData.value = []
  dataY.value = []
  InsectAPI.insectDist({ year: year.value, insectNames: insectName.value?.length > 0 ? insectName.value.join(',') : '' }).then((ress) => {
    xData.value = []
    const res = ress?.lines || []
    const total = 0
    for (const key in res) {
      const x = []
      if (Array.isArray(res[key])) {
        const y = []
        for (const item of res[key]) {
          x.push(item.x)
          y.push(item.y)
          xData.value.push(item.x)
        }

        dataY.value.push({ name: key, data: res[key], x, total, y })
      }
    }

    // 去重并排序
    xData.value = [...new Set(xData.value)]

    // 去重并排序
    xData.value = [...new Set(xData.value)].sort()

    optionList.value = ress?.options?.map(item => ({ label: item, value: item })) || []
    insectName.value = ress?.selected || []
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

const option = computed(() => {
  return {
    grid: {
      top: '10%',
      bottom: '12%',
      left: '4%',
      right: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: true,
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#789590',
      },
      name: '单位:只',
      nameLocation: 'end',
      nameRotate: 0,
      nameTextStyle: {
        color: '#789590',
        lineHeight: -5,
        verticalAlign: 'bottom',
        fontSize: 9,
      },
      z: 10,
      zlevel: 10,
    },
    // legend: {
    //   show: true,
    //   right: 0,
    //   icon: 'roundRect',
    //   itemWidth: 10,
    //   itemHeight: 6,
    //   itemGap: 30,
    // },
    dataZoom: [
      {
        type: 'inside',
      },
    ],
    series: computed(() => {
      // 动态生成 dataY 中所有数据对应的折线配置
      // 为了确保循环的数据能正确刷新，使用 computed 来处理 dataY 的映射
      const colors = ['#3DFCCB', '#0099F9', '#2ED372', '#CE54EC', '#D9F421', '#72D2FF', '#FFC72F', '#FF691F'] // 预设颜色数组
      return dataY.value.map((item, index) => {
        const color = colors[index % colors.length] // 循环使用颜色
        return {
          name: item.name || '',
          type: 'bar',
          stack: 'total',
          // barWidth: '10%',
          barWidth: '10', // 设置柱子宽度为 10%
          // 计算每个柱子的百分比
          data: item.y,
          color,
        }
      })
    }).value,
  } as EChartsOption
})

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card title="数量分布" class="h-full">
    <header class="flex justify-end mt--10">
      <!--      <SelectYearRange v-model="yearRange" :clearable="true" @change="handleYearChange" /> -->
      <SelectDate v-model="year" @change="handleQuery" />
      <Select v-model="insectName" :options="optionList" multiple :props="{ label: 'label', value: 'value' }" @change="changeSelete" />
    </header>
    <div v-loading="loading" class="hMani">
      <Chart v-if="xData.length" :option="option" />
      <div v-else class="empty" />
    </div>
  </Card>
</template>

<style scoped lang="scss">
:deep(.select-bg) {
  min-width: 120px;
  height: 40px;
  padding-top: 11px;
  font-size: 14px;
  background: url(@/assets/common/select-bg.webp);

  .svg-icon {
    margin-right: 14px;
  }
}

.hMani {
  height: var(--chart-height);
}
</style>
