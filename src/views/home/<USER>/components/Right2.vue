<script setup lang="ts">
import type { DeviceParam } from '@/api/farmland/type'
import img2 from '@/assets/home/<USER>/<EMAIL>'
import img1 from '@/assets/home/<USER>/<EMAIL>'

const { insectCountList, loading } = defineProps<{ insectCountList: DeviceParam[], loading: boolean }>()

const iconMap = {
  9991: img1,
  9992: img2,
}
</script>

<template>
  <Card title="今年病虫监测">
    <section v-loading="loading" class="h-scroll">
      <div v-if="insectCountList.length" class="gap-15" grid="~ cols-2">
        <div v-for="item in insectCountList" :key="item.paramCode" class="item flex bg-#004831/25 h-90">
          <aside class="relative shrink-0 w-90 h-90">
            <img :src="iconMap[item.paramCode]" :alt="item.paramName" class="absolute left-1/2 top-1/2 -translate-1/2 w-128 h-128" draggable="false" />
          </aside>
          <div class="flex flex-col justify-center gap-11 -ml-7">
            <header class="title" text="14 #CEFFE6">{{ item.paramName }}</header>
            <footer v-if="item.value" text="#CEFFE6">
              <span class="value font-din font-bold" text="16">{{ item.value }}</span>
              <span class="unit" text="13">{{ item.unit }}</span>
            </footer>
            <footer v-else text="#CEFFE6"> -- </footer>
          </div>
        </div>
      </div>
      <div v-else class="empty h-full" />
    </section>
  </Card>
</template>

<style scoped lang="scss">
.item {
  .value,
  .unit {
    background: linear-gradient(0deg, #00d851 0%, #fff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.h-scroll {
  height: 100px;
}
</style>
