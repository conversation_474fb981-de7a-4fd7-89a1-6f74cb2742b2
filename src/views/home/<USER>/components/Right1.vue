<script setup lang="ts">
import type { DeviceParam } from '@/api/farmland/type'

import npk from '@/assets/home/<USER>/icon_danlin<PERSON><EMAIL>'
import nitrogen from '@/assets/home/<USER>/icon_danlizhi.webp'
import air_rh from '@/assets/home/<USER>/<EMAIL>'
import air_temp from '@/assets/home/<USER>/<EMAIL>'
import conductivity from '@/assets/home/<USER>/<EMAIL>'
import batteryLevel from '@/assets/home/<USER>/<EMAIL>'
import batteryVoltage from '@/assets/home/<USER>/<EMAIL>'
import rainfall from '@/assets/home/<USER>/<EMAIL>'
import co2 from '@/assets/home/<USER>/<EMAIL>'
import wind_speed from '@/assets/home/<USER>/<EMAIL>'
import wind_direction from '@/assets/home/<USER>/<EMAIL>'
import radiation_accumulated from '@/assets/home/<USER>/<EMAIL>'
import photosynthesis from '@/assets/home/<USER>/<EMAIL>'
import kalium from '@/assets/home/<USER>/<EMAIL>'
import phosphorus from '@/assets/home/<USER>/icon_linlizhi.webp'
import ph from '@/assets/home/<USER>/<EMAIL>'
import pm2_5 from '@/assets/home/<USER>/<EMAIL>'
import pm10 from '@/assets/home/<USER>/<EMAIL>'
import sunshine_hours from '@/assets/home/<USER>/<EMAIL>'
import pressure from '@/assets/home/<USER>/<EMAIL>'
import moisture from '@/assets/home/<USER>/<EMAIL>'
import temperature from '@/assets/home/<USER>/<EMAIL>'
import salt from '@/assets/home/<USER>/<EMAIL>'
import water_level from '@/assets/home/<USER>/<EMAIL>'
import rainfall_accumulated from '@/assets/home/<USER>/<EMAIL>'
import illuminance from '@/assets/home/<USER>/<EMAIL>'
import evaporation from '@/assets/home/<USER>/<EMAIL>'
import uv_index from '@/assets/home/<USER>/<EMAIL>'
import solar_radiation from '@/assets/home/<USER>/<EMAIL>'

const { soilList, loading } = defineProps<{ soilList: DeviceParam[], loading: boolean }>()

const iconMap = {
  temperature,
  moisture,
  salt,
  conductivity,
  ph,
  nitrogen,
  phosphorus,
  kalium,
  npk,
  batteryLevel,
  batteryVoltage,
  air_temp,
  air_rh,
  pressure,
  illuminance,
  co2,
  pm2_5,
  pm10,
  wind_speed,
  wind_direction,
  solar_radiation,
  radiation_accumulated,
  rainfall,
  rainfall_accumulated,
  evaporation,
  sunshine_hours,
  photosynthesis,
  water_level,
  uv_index,
}
</script>

<template>
  <Card v-loading="loading" title="今年气象墒情">
    <el-scrollbar v-if="soilList.length">
      <div class="gap-15" grid="~ cols-2">
        <div v-for="item in soilList" :key="item.paramCode" class="item flex bg-#004831/25 h-90">
          <aside class="relative shrink-0 w-90 h-90">
            <img :src="iconMap[item.icon]" :alt="item.paramName" class="absolute left-1/2 top-1/2 -translate-1/2 w-128 h-128" draggable="false" />
          </aside>
          <div class="flex flex-col justify-center gap-11 -ml-7">
            <header class="title" text="14 #CEFFE6">{{ item.paramName }}</header>
            <footer text="#CEFFE6">
              <span class="value font-din font-bold" text="16">{{ item.value }}</span>
              <span class="unit" text="13">{{ item.unit }}</span>
            </footer>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div v-else class="empty" />
  </Card>
</template>

<style scoped lang="scss">
.item {
  .value,
  .unit {
    background: linear-gradient(0deg, #00d851 0%, #fff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.h-scroll {
  height: calc(100% - 1.5vw);
}
</style>
