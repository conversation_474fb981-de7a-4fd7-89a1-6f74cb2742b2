<script setup lang="ts">
import type { Summary } from '@/api/lamp/type'
import { LampAPI } from '@/api/lamp/lamp.ts'
import { useAppStore } from '@/store/app.ts'

const appStore = useAppStore()
const data = ref<Summary>({})
const loading = ref(false)
function handleQuery() {
  loading.value = true
  LampAPI.pestAnnual({ farmlandId: appStore.farmlandId, deviceId: appStore.deviceId }).then((res) => {
    data.value = res || {}
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card v-loading="loading" title="病虫与环境数据统计" class="h-full">
    <div class="flex justify-between items-center pl-15 box-border mt-20 mb-20">
      <div class=" text-center">
        <img src="@/assets/device/scl.png" alt="" class="w-91 h-83" draggable="false" />
        <div class="text-#B9DCCA text-15 mt-8 mb-13">平均开灯时长</div>
        <CountTo class="numText" :start-val="0" :end-val="data.lightingTime" />
        <span class="numText" style="font-size: 18px;">h</span>
      </div>
      <div class="text-center">
        <img src="@/assets/device/pjwd.png" alt="" class="w-91 h-83" draggable="false" />
        <div class="text-#B9DCCA text-15  mt-8 mb-13">今年年平均温度</div>
        <CountTo class="numText" :start-val="0" :end-val="data.temperature" />
        <span class="numText" style="font-size: 18px;">℃</span>
      </div>
      <div class="text-center">
        <img src="@/assets/device/pjsd.png" alt="" class="w-91 h-83" draggable="false" />
        <div class="text-#B9DCCA text-15 mt-8 mb-13">今年年平均湿度</div>
        <CountTo class="numText" :start-val="0" :end-val="data.humidity" />
        <span class="numText" style="font-size: 18px;">%</span>
      </div>
    </div>
  </Card>
</template>

<style scoped lang="scss">
.numText {
  font-family: DIN;
  font-size: 36px;
  font-weight: 500;
  color: #fbfffd;
  background: linear-gradient(180deg, #fbfffd 0%, #58ffba 100%);
  background-clip: text;
  opacity: 0.96;
  -webkit-text-fill-color: transparent;
}
</style>
