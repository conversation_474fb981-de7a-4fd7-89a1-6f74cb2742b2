<script setup lang="ts">
import dayjs from 'dayjs'
import { ref } from 'vue'
import { SoilAPI } from '@/api/soil/soil.ts'
import { useAppStore } from '@/store/app.ts'

const currentMonth = dayjs(new Date()).format('YYYY-MM')
const date = ref(currentMonth)
function dateChange() {
  list.value = []
  handleQuery()
}

const list = ref([])

const appStore = useAppStore()
const loading = ref(false)
// 温度对比
function handleQuery() {
  loading.value = true
  SoilAPI.paramAvg({
    farmlandId: appStore.farmlandId,
    deviceId: appStore.deviceId,
    findDate: date.value ? `${date.value}-01` : '',
  }).then((res) => {
    list.value = res || []
    console.log(list.value, '===============')
  }).finally(() => {
    loading.value = false
  })
}

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  handleQuery()
})
</script>

<template>
  <Card title="墒情年平均统计分析" class="h-full relative">
    <SelectDate v-model="date" class="!absolute -right-15 top-1.7vh" type="month" @change="dateChange" />
    <div v-loading="loading" class="" p="0">
      <div class="flex items-center h-56 px-10" text="14 #3DFCCB">
        <div class="w-30% text-left">参数名称</div>
        <div class="flex-1 text-center">上月平均值</div>
        <div class="flex-1 text-center">本月平均值</div>
        <div class="flex-1 text-center">同比变化</div>
      </div>
      <AutoScroll v-if="list?.length > 0" :list="list" class="!h-38vh">
        <div v-for="(item, index) in list" :key="item.type" class="item flex items-center h-56 px-10" text="16 #CEFFE6">
          <div class="w-30% flex items-center justify-start">
            <div class="index shrink-0 w-22 h-14 grid place-items-center mr-6" text="12 #FFFFFF">{{ index + 1 }}</div>
            <div class="" text="16 #CEFFE6">{{ item.paramName }}</div>
          </div>
          <div class="flex-1 w-22 h-14 mr-6 text-center">
            <span v-if="item?.preAvg || item?.preAvg === 0">{{ item?.preAvg }} </span>
            <span v-else> -- </span>
            <span class="text-12">{{ item?.unit }}</span>
          </div>
          <div class="flex-1 text-center">
            <span v-if="item?.nowAvg || item?.nowAvg === 0">{{ item?.nowAvg }}</span>
            <span v-else> -- </span>
            <span class="text-12">{{ item?.unit }}</span>
          </div>
          <div class="flex-1 text-center yearChange">
            <span v-if="[null, undefined].includes(item.yoy)" class="yearChange">--</span>
            <span v-if="item?.yoy > 0 || item?.yoy == 0" class="yearChange1">{{ item?.yoy }}%</span>
            <span v-if="item?.yoy && item?.yoy < 0" class="yearChange">{{ item?.yoy }}%</span>
            <!-- 添加一个向下箭头的图标，这里使用 Unicode 字符 ↓ -->
            <span v-if="item?.yoy < 0" class="yearChange">↓</span>
            <!-- 添加一个向上箭头的图标，这里使用 Unicode 字符 ↑ -->
            <span v-if="item?.yoy > 0" class="yearChange1">↑</span>
          </div>
        </div>
      </AutoScroll>
      <div v-else class="empty pt-100" />
    </div>
  </Card>
</template>

<style scoped lang="scss">
:deep(.select-bg) {
  min-width: 120px;
  height: 40px;
  padding-top: 11px;
  font-size: 14px;
  background: url(@/assets/common/select-bg.webp);

  .svg-icon {
    margin-right: 14px;
  }
}

.item {
  margin-bottom: 16px;
  // rgba 函数的第四个参数是透明度，取值范围是 0 到 1，原代码多写了一个参数，正确写法如下
  background: rgba(0, 72, 49, 0.25);

  &:nth-child(1) {
    .index {
      background: url(@/assets/device/bgIndex1.png) center / 100% 100% no-repeat;
      // color: white;
    }
  }

  .yearChange {
    // width: 28px;
    // height: 13px;
    font-family: DIN;
    font-size: 16px;
    font-weight: 500;
    color: #cde8fd;
    background: linear-gradient(0deg, #fcc84f 0%, #f9dfa3 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .yearChange1 {
    // width: 28px;
    // height: 13px;
    font-family: DIN;
    font-size: 16px;
    font-weight: 500;
    color: #cde8fd;
    background: linear-gradient(0deg, #fcc84f 0%, #48ea00 0%, #c1ffa8 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  &:not(:nth-child(1)) {
    .index {
      background: url(@/assets/device/bgIndex2.png) center / 100% 100% no-repeat;
      // color: white;
    }
  }

  // &:nth-child(2) {
  //   .index {
  //     background: url(@/assets/workbench/2.webp) center / 100% 100% no-repeat;
  //     color: white;
  //   }
  // }

  // &:nth-child(3) {
  //   .index {
  //     background: url(@/assets/workbench/3.webp) center / 100% 100% no-repeat;
  //     color: white;
  //   }
  // }

  // &:nth-child(2n) {
  //   background: transparent;
  // }
}

.hMani {
  height: var(--scroll-height) !important;
  // height: 100px;
}
</style>
