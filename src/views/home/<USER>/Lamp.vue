<script setup lang="ts">
import type { DeviceOption } from '@/api/common/type'
import { storeToRefs } from 'pinia'
import { CommonAPI } from '@/api/common/common.ts'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'
import Left1 from './components/Left1.vue'
import Left2 from './components/Left2.vue'
import Left3 from './components/Left3.vue'
import Right1 from './components/Right1.vue'
import Right2 from './components/Right2.vue'
import Right3 from './components/Right3.vue'

const appStore = useAppStore()
const { farmlandId, deviceId } = storeToRefs(appStore)
deviceId.value = null
function changeFarmlandId(id: number) {
  emitter.emit('selectFarmlandById', id)
}

const deviceOption = ref<DeviceOption[]>([])
CommonAPI.deviceOption({ deviceType: 1 }).then((res) => {
  deviceOption.value = res
})
</script>

<template>
  <PageLayout class="lamp">
    <template #left>
      <div class="flex flex-col h-full pb-10">
        <Left1 class="flex-1" />
        <Left2 class="flex-1" />
        <Left3 class="flex-1" />
      </div>
    </template>
    <template #left-control>
      <div class="flex">
        <Select v-model="farmlandId" clearable :options="appStore.farmlandList" @change="changeFarmlandId" />
        <Select v-model="deviceId" clearable :options="deviceOption" :props="{ value: 'deviceId', label: 'serialNum' }" />
      </div>
    </template>
    <template #right>
      <div class="flex flex-col h-full pb-10">
        <Right1 class="flex-1" />
        <Right2 class="flex-1" />
        <Right3 class="flex-1" />
      </div>
    </template>
  </PageLayout>
</template>

<style scoped lang="scss">
.lamp {
  --chart-height: calc((100vh - 200px) / 3 - var(--card-title-height) - 10px);
}
</style>
