<script setup lang="ts">
import type { Statistics } from '@/api/overview/type'
import { OverviewAPI } from '@/api/overview/overview.ts'
import icon1 from '@/assets/home/<USER>/1.webp'
import icon3 from '@/assets/home/<USER>/2.webp'
import icon2 from '@/assets/home/<USER>/3.webp'
import icon4 from '@/assets/home/<USER>/4.webp'
import CountTo from '@/components/CountTo/CountTo.vue'
import { useOn } from '@/hooks/useOn.ts'
import { emitter } from '@/utils/events.ts'
import Farmland from '@/views/home/<USER>/Farmland.vue'

const iconMap = {
  1: icon1,
  2: icon2,
  3: icon3,
  4: icon4,
}

const list = ref<Statistics[]>([])
const loading = ref(false)
function handleQuery() {
  loading.value = true
  OverviewAPI.statistics().then((res) => {
    list.value = res
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

const selectedFarmland = ref<number>()
useOn(emitter, 'farmlandClick', (id: number) => {
  selectedFarmland.value = null
  nextTick(() => {
    selectedFarmland.value = id
  })
})
</script>

<template>
  <!--  地块详情 -->
  <Farmland v-if="selectedFarmland" :id="selectedFarmland" />
  <PageLayout v-else class="home">
    <template #left>
      <div v-loading="loading" class="h-full flex flex-col gap-y-5 pl-36 pt-55 pointer-events-none">
        <div v-for="item in list" :key="item.type" class="flex pointer-events-auto">
          <img :src="iconMap[item.type]" :alt="item.typeName" class="w-148 h-149" draggable="false" />
          <aside class="flex flex-col">
            <header class="mt-37" text="#CEFFE6">{{ item.typeName }}</header>
            <img src="@/assets/home/<USER>/line.webp" alt="" class="w-104 h-2 mt-7 mb-12" draggable="false" />
            <div class="">
              <span class="value font-din font-bold" text="40 #fbfffd/96">
                <CountTo :start-val="0" :end-val="item.onlineCount" />
                <span>/{{ item.total }}</span>
              </span>
              <span class="ml-16" text="14 #ABCCBB/96">个</span>
            </div>
          </aside>
        </div>
      </div>
    </template>
  </PageLayout>
</template>

<style scoped lang="scss">
.value {
  background: linear-gradient(180deg, white 0%, #58ffba 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.home {
  :deep(.right) {
    width: 0;
  }
}
:deep(.right) {
  pointer-events: none;
}
</style>
