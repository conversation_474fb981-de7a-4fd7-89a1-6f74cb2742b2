<script setup lang="ts">
import type { DeviceOption } from '@/api/common/type'
import { storeToRefs } from 'pinia'
import { CommonAPI } from '@/api/common/common.ts'
import { SoilAPI } from '@/api/soil/soil.ts'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events.ts'
import Bottom from './components/Bottom.vue'
import Left1 from './components/Left1.vue'
import Left2 from './components/Left2.vue'
import Right1 from './components/Right1.vue'

const appStore = useAppStore()
const { farmlandId, deviceId } = storeToRefs(appStore)
deviceId.value = null
/**
 * 通过select切换地块
 */
function changeFarmlandId(id: number) {
  emitter.emit('selectFarmlandById', id)
}

/**
 * 通过select切换设备（墒情2）
 */
const deviceOption = ref<DeviceOption[]>([])
CommonAPI.deviceOption({ deviceType: 2 }).then((res) => {
  deviceOption.value = res
  deviceId.value = res[0]?.deviceId
})

const weatherLoading = ref(false)
const weatherData = ref()
const weatherList = ref([])
const location = ref('')

/* 天气趋势获取 */
function getWeather() {
  weatherLoading.value = true
  CommonAPI.weather({ historyDays: 10, featureDays: 16 })
    .then((res) => {
      weatherData.value = res ?? []
      weatherList.value = res?.points ?? []
      location.value = res?.location ?? ''
    })
    .finally(() => {
      weatherLoading.value = false
    })
}

watch(() => [appStore.farmlandId, appStore.deviceId], () => {
  getWeather()
})
</script>

<template>
  <div class="flex flex-col">
    <PageLayout class="grow overflow-hidden soil">
      <template #left>
        <div class="flex flex-col h-full pb-10">
          <Left1 class="flex-1" />
          <Left2 class="flex-1" />
        </div>
      </template>
      <template #left-control>
        <div class="flex">
          <Select v-model="farmlandId" clearable :options="appStore.farmlandList" @change="changeFarmlandId" />
          <Select v-model="deviceId" :options="deviceOption" :props="{ value: 'deviceId', label: 'serialNum' }" />
        </div>
      </template>
      <template #right>
        <div class="flex flex-col h-full">
          <Right1 class="flex-1" />
        </div>
      </template>
    </PageLayout>
    <Bottom v-loading="weatherLoading" class="bounceInUp h-31vh shrink-0" :weatherDataD="weatherData" :weatherList="weatherList" :location="location" />
  </div>
</template>

<style
  scoped
  lang="scss"
></style>

<style lang="scss">
.plantDialog.plantDialog {
  --el-dialog-width: 668px;

  height: 737px;
  padding: 35px;
  background: url(@/assets/common/bg_668.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}

.time-line {
  position: relative;

  &::before {
    position: absolute;
    top: 20px;
    bottom: 0;
    left: 27px;
    z-index: 0;
    content: '';
    border-left: 1px dashed #364d3c;
  }
}

.status {
  background: url(@/assets/home/<USER>/ 100% no-repeat;
}

.soil {
  --chart-height: calc((100vh - 200px - 31vh) / 2 - var(--card-title-height) - 10px);
  --scroll-height: calc((100vh - 200px - 31vh) / 1 - var(--card-title-height) - 10px);
}
</style>
