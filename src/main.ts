import { dayjs } from 'element-plus'
import { createApp } from 'vue'
import router from '@/router'
import { setupStore } from '@/store'
import App from './App.vue'
import '@/style/index.scss'
import 'normalize.css'
// 原子化css
import 'uno.css'
import './permission'
import 'element-plus/dist/index.css'
// svg-icon
import 'virtual:svg-icons-register'
import 'animate.css'
import 'dayjs/locale/zh-cn'
import 'leaflet/dist/leaflet.css'

const app = createApp(App)
setupStore(app)
dayjs.en.weekStart = 1

app.use(router)
app.mount('#app')
