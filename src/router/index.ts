import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHashHistory } from 'vue-router'
import { KN<PERSON>LEDGE_EXERT, KNOWLEDGE_MODEL, KNOWLEDGE_PEST, KNOWLEDGE_PUBLISH, KNOWLEDGE_TECHNOLOGY } from '@/constants'
import Layout from '@/layout/Layout.vue'

export type RouteRecordRawExt = RouteRecordRaw & { hidden?: boolean, children?: RouteRecordRawExt[] }

export const baseRoutes: Array<RouteRecordRawExt> = [
  {
    path: '/redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/Redirect.vue'),
      },
    ],
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/404.vue'),
  },
  {
    path: '/',
    redirect: '/home',
  },

]

export const appRoutes: Array<RouteRecordRawExt> = [
  {
    path: '/home',
    component: Layout,
    meta: { title: '首页' },
    redirect: '/home/<USER>',
    children: [
      { path: '/home/<USER>', meta: { title: '总览', deviceType: null }, component: () => import('@/views/home/<USER>/Overview.vue') },
      { path: '/home/<USER>', meta: { title: '智慧杀虫灯', deviceType: 1 }, component: () => import('@/views/home/<USER>/Lamp.vue') },
      { path: '/home/<USER>', meta: { title: '虫情分析仪', deviceType: 3 }, component: () => import('@/views/home/<USER>/Insect.vue') },
      { path: '/home/<USER>', meta: { title: '墒情气象一体机', deviceType: 2 }, component: () => import('@/views/home/<USER>/Soil.vue') },
      { path: '/home/<USER>', meta: { title: '苗情监控', deviceType: 4 }, component: () => import('@/views/home/<USER>/Monitor.vue') },
    ],
  },
  {
    path: '/knowledge',
    component: Layout,
    meta: { title: '知识库' },
    children: [
      { path: '/knowledge/home', meta: { title: '总览', noMap: true }, component: () => import('@/views/knowledge/Knowledge.vue') },
      { path: '/knowledge/questionAnswering', meta: { title: '农业大模型', noMap: true }, component: () => import('@/views/knowledge/questionAnswering.vue') },
      { path: '/knowledge/specialistList', meta: { title: '专家工作站', noMap: true }, component: () => import('@/views/knowledge/specialistList.vue') },
      { path: '/knowledge/storeContent', meta: { title: '知识库详情', noMap: true }, component: () => import('@/views/knowledge/storeContent.vue') },

    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes: [...baseRoutes, ...appRoutes],
})
export const asyncRoutes: Array<RouteRecordRawExt> = []

export default router
