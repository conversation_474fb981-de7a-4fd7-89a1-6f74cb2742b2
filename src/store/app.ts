import type { Farmland } from '@/api/common/type'
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 当前页面尺寸相对于1920的缩放比例
    ratio: getRatio(),
    // 地块列表
    farmlandList: [] as Farmland[],
    // 当前选中地块id
    farmlandId: null as number,
    // 当前选择设备
    deviceId: null as number,
    // 已有设备类型，没有的不显示菜单
    hasDeviceTypes: {} as Record<string, boolean>,
  }),
  actions: {},
})

/**
 * 获取相对于1920的缩放比例
 */
export function getRatio(): number {
  const width = document.documentElement.clientWidth
  return width / 1920
}
