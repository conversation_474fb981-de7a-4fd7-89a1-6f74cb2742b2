import type { UserInfo } from '@/api/system/user'
import { defineStore } from 'pinia'
import { AuthAPI } from '@/api/system/auth'
import { FileAPI } from '@/api/system/file.ts'
import { UserAPI } from '@/api/system/user'
import { DOMAIN } from '@/constants'
import { store } from '@/store/index'
import { removeToken } from '@/utils/auth.ts'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: { roles: [], perms: [] } as UserInfo,
  }),
  actions: {

    getUserInfo() {
      return new Promise<UserInfo>((resolve, reject) => {
        UserAPI.getInfo()
          .then((data) => {
            if (!data) {
              reject(new Error('Verification failed, please Login again.'))
              return
            }

            if (!data.roles || data.roles.length <= 0) {
              reject(new Error('getUserInfo: roles must be a non-null array!'))
              return
            }

            this.user = data

            FileAPI.init().then(() => {
              resolve(data)
            })
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    logout() {
      return new Promise<void>((resolve, reject) => {
        AuthAPI.logout()
          .then(() => {
            removeToken()
            window.location.replace(`${DOMAIN}/#/login?clearRoute=true&bigData=true`)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

  },
})

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
