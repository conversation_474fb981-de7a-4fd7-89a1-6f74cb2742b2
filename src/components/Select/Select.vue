<script setup lang="ts">
interface PropsInner {
  label: string
  value: string
}
interface SelectProps1 {
  options: any[]
  props?: PropsInner
  clearable?: boolean
  multiple?: boolean
  filterable?: boolean
}

const { options, props = { label: 'name', value: 'id' }, clearable, multiple, filterable = true } = defineProps<SelectProps1>()

const emit = defineEmits(['change', 'clear'])

const value = defineModel()

function change(val: any) {
  emit('change', val)
}
</script>

<template>
  <div>
    <el-select v-if="multiple" v-model="value" class="multiple" :clearable="clearable" multiple collapse-tags collapse-tags-tooltip placeholder="请选择" size="large" @change="change" @clear="emit('clear')">
      <el-option v-for="item in options" :key="item[props.value]" :label="item[props.label]" :value="item[props.value]" class="max-w-300" />
    </el-select>
    <el-select v-else v-model="value" :filterable="filterable" :clearable="clearable" placeholder="请选择" size="large" @change="change" @clear="emit('clear')">
      <el-option v-for="item in options" :key="item[props.value]" :label="item[props.label]" :value="item[props.value]" class="max-w-300" />
    </el-select>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-select) {
  .el-select__placeholder {
    font-size: 12px;
    color: #ceffe6;
  }

  .el-select__wrapper {
    width: 129px;
    height: 39px;
    padding-top: 4px;
    background: url(@/assets/common/select-bg.webp) center / 100% 100% no-repeat;
    box-shadow: none !important;
  }

  .el-select__icon {
    color: #ceffe6;

    &:not(.el-select__clear) {
      background: url(@/icons/down-arrow.svg) center / 80% 50% no-repeat;

      svg {
        display: none;
      }
    }
  }

  .el-tag {
    padding: 0;
    color: #d9fff3;
    background-color: transparent;

    .el-tag__close {
      margin-left: 0;
      color: #d9fff3;
    }

    &.is-closable {
      max-width: 60px !important;
    }
  }

  &.multiple {
    .el-select__selection {
      flex-wrap: nowrap;
      gap: 0;
      padding-left: 7px;
      overflow: hidden;
    }
  }
}
</style>
