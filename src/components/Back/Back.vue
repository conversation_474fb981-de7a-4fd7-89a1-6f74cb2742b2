<script lang='ts' setup>
import { useRouter } from 'vue-router'

const router = useRouter()
function goBack() {
  router.back()
}
</script>

<template>
  <div class="back w-139 h-57 flex justify-center items-center color-#E2FFF0 font-bold font-alimama cursor-pointer" @click="goBack">返回</div>
</template>

<style scoped lang='scss'>
.back {
  background: url(@/assets/layout/menu-bg-active.webp) no-repeat;
  background-size: 100% 100%;
}
</style>
