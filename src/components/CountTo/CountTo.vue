<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

/**
 * 该组件是一个数字计数器，支持从一个数字平滑过渡到另一个数字。
 * 从网上copy而来，文档可通过下面链接查看
 * @see https://github.com/PanJiaChen/vue-countTo/tree/master
 */
interface CountToProps {
  startVal?: number
  endVal?: number
  duration?: number
  autoplay?: boolean
  decimals?: number
  decimal?: string
  separator?: string
  prefix?: string
  suffix?: string
  useEasing?: boolean
  easingFn?: (t: number, b: number, c: number, d: number) => number
}

const props = withDefaults(defineProps<CountToProps>(), {
  startVal: 0,
  endVal: 0,
  duration: 3000,
  autoplay: true,
  decimals: 0,
  decimal: '.',
  separator: ',',
  prefix: '',
  suffix: '',
  useEasing: true,
  easingFn: (t: number, b: number, c: number, d: number) => {
    return (c * (-(2 ** ((-10 * t) / d)) + 1) * 1024) / 1023 + b
  },
})

const emit = defineEmits<{
  (e: 'mountedCallback'): void
  (e: 'callback'): void
}>()

// 响应式状态
const localStartVal = ref(props.startVal)
const displayValue = ref(formatNumber(props.startVal))
const printVal = ref<number | null>(null)
const paused = ref(false)
const localDuration = ref(props.duration)
const startTime = ref<number | null>(null)
const timestamp = ref<number | null>(null)
const remaining = ref<number | null>(null)
let rAF: number | null = null

// 计算属性
const countDown = computed(() => props.startVal > props.endVal)

// 观察属性变化
watch(
  () => props.startVal,
  () => {
    if (props.autoplay) {
      start()
    }
  },
)

watch(
  () => props.endVal,
  () => {
    if (props.autoplay) {
      start()
    }
  },
)

// 生命周期钩子
onMounted(() => {
  if (props.autoplay) {
    start()
  }

  emit('mountedCallback')
})

onUnmounted(() => {
  if (rAF !== null) {
    cancelAnimationFrame(rAF)
  }
})

// 方法
function start(): void {
  localStartVal.value = props.startVal
  startTime.value = null
  localDuration.value = props.duration
  paused.value = false
  rAF = requestAnimationFrame(count)
}

function pauseResume(): void {
  if (paused.value) {
    resume()
    paused.value = false
  }
  else {
    pause()
    paused.value = true
  }
}

function pause(): void {
  if (rAF !== null) {
    cancelAnimationFrame(rAF)
  }
}

function resume(): void {
  startTime.value = null
  if (remaining.value !== null && printVal.value !== null) {
    localDuration.value = +remaining.value
    localStartVal.value = +printVal.value
    rAF = requestAnimationFrame(count)
  }
}

function reset(): void {
  startTime.value = null
  if (rAF !== null) {
    cancelAnimationFrame(rAF)
  }

  displayValue.value = formatNumber(props.startVal)
}

function count(timestampValue: number): void {
  if (!startTime.value)
    startTime.value = timestampValue
  timestamp.value = timestampValue
  const progress = timestamp.value - startTime.value
  remaining.value = localDuration.value - progress

  if (props.useEasing) {
    printVal.value = countDown.value
      ? localStartVal.value - props.easingFn(progress, 0, localStartVal.value - props.endVal, localDuration.value)
      : props.easingFn(
          progress,
          localStartVal.value,
          props.endVal - localStartVal.value,
          localDuration.value,
        )
  }
  else {
    printVal.value = countDown.value
      ? localStartVal.value - (localStartVal.value - props.endVal) * (progress / localDuration.value)
      : localStartVal.value + (props.endVal - localStartVal.value) * (progress / localDuration.value)
  }

  printVal.value = countDown.value ? Math.max(printVal.value, props.endVal) : Math.min(printVal.value, props.endVal)

  displayValue.value = formatNumber(printVal.value)

  if (progress < localDuration.value) {
    rAF = requestAnimationFrame(count)
  }
  else {
    emit('callback')
  }
}

function isNumber(val: any): boolean {
  return !isNaN(Number.parseFloat(val))
}

function formatNumber(num: number): string {
  const fixedNum = num.toFixed(props.decimals)
  const numString = `${fixedNum}`
  const x = numString.split('.')
  let x1 = x[0]
  const x2 = x.length > 1 ? props.decimal + x[1] : ''
  const rgx = /(\d+)(\d{3})/

  if (props.separator && !isNumber(props.separator)) {
    while (rgx.test(x1)) {
      x1 = x1.replace(rgx, `$1${props.separator}$2`)
    }
  }

  return props.prefix + x1 + x2 + props.suffix
}

// 暴露方法给父组件
defineExpose({
  start,
  pauseResume,
  pause,
  resume,
  reset,
})
</script>

<template>
  <span>{{ displayValue }}</span>
</template>
