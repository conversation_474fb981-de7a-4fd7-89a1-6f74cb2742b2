<script setup lang="ts">
import type { DateType2 } from '@/constants'
import dayjs from 'dayjs'

const { type = 'year' } = defineProps<{ type?: DateType2 }>()
const emit = defineEmits(['change'])

const value = defineModel<string[]>()

const weekDate = ref(value.value[0])
function change() {
  const [startDate, endDate] = value.value
  switch (type) {
    case 'day':
      value.value = [dayjs(startDate).format('YYYY-MM-DD 00:00:00'), dayjs(endDate).format('YYYY-MM-DD 23:59:59')]
      break
    case 'week':
      value.value = [weekDate.value, dayjs(weekDate.value).endOf('week').format('YYYY-MM-DD')]
      break
    case 'month':
      value.value = [dayjs(startDate).startOf('month').format('YYYY-MM-DD'), dayjs(endDate).endOf('month').format('YYYY-MM-DD')]
      break
    case 'year':
      value.value = [dayjs(startDate).startOf('year').format('YYYY-MM-DD'), dayjs(endDate).endOf('year').format('YYYY-MM-DD')]
      break
  }

  emit('change', value.value)
}

const labelFormatMap: Record<DateType2, string> = {
  day: 'YYYY-MM-DD',
  year: 'YYYY',
  month: 'YYYY-MM',
  week: 'YYYY 第 ww 周',
}

watch(
  () => type,
  () => {
    switch (type) {
      case 'day':
        value.value = [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')]
        break
      case 'week':
        weekDate.value = dayjs().format('YYYY-MM-DD')
        value.value = [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')]
        break
      case 'month':
        value.value = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
        break
      case 'year':
        value.value = [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')]
        break
    }

    emit('change', value.value)
  },
)
function disabledDate(date: Date) {
  return date > new Date()
}
</script>

<template>
  <div class="relative pointer-events-auto w-fit h-fit">
    <el-date-picker
      v-if="type === 'week'"
      v-model="weekDate"
      value-format="YYYY-MM-DD"
      type="week"
      class="h-full !w-full opacity-0 !absolute inset-0 !cursor-pointer"
      :clearable="false"
      :disabled-date="disabledDate"
      @change="change"
    />

    <el-date-picker
      v-else
      v-model="value"
      value-format="YYYY-MM-DD"
      :type="`${type === 'day' ? 'date' : type}range`"
      class="h-full !w-full opacity-0 !absolute inset-0 !cursor-pointer"
      :clearable="false"
      :disabled-date="disabledDate"
      @change="change"
    />
    <div class="select-bg use-bg min-w-190 h-45 flex pt-13 pl-20 tracking-1 justify-center pointer-events-none" text="14 #CEFFE6">
      <span />
      <span v-if="type === 'week'" class="truncate" text="center">{{ dayjs(value[0]).format(labelFormatMap[type]) }} </span>
      <span v-else class="truncate mr-20" text="center">{{ dayjs(value[0]).format(labelFormatMap[type]) }} ~ {{ dayjs(value[1]).format(labelFormatMap[type]) }}</span>
      <SvgIcon name="date" class="text-14 ml-auto mr-24" />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input__inner, ) {
  cursor: pointer;
}

:deep(.el-input__wrapper) {
  cursor: pointer;
}

.select-bg {
  background: url(@/assets/common/select-bg-190.webp);
}
</style>
