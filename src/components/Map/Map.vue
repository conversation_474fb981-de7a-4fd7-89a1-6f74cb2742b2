<script lang="ts" setup>
import type { CRSTypes } from 'gcoord'
import type { Device, Farmland } from '@/api/common/type'
import type { DeviceType } from '@/constants'
import gcoord from 'gcoord'

import L from 'leaflet'
import { CommonAPI } from '@/api/common/common.ts'
import imgOffline1 from '@/assets/map/deviceIcons/1-offline.webp'
import img1 from '@/assets/map/deviceIcons/1.webp'
import imgOffline3 from '@/assets/map/deviceIcons/2-offline.webp'
import img3 from '@/assets/map/deviceIcons/2.webp'
import imgOffline2 from '@/assets/map/deviceIcons/3-offline.webp'
import img2 from '@/assets/map/deviceIcons/3.webp'
import imgOffline4 from '@/assets/map/deviceIcons/4-offline.webp'
import img4 from '@/assets/map/deviceIcons/4.webp'
import InsectDialog from '@/components/Map/DeviceDetailDialog/InsectDialog.vue'
import LampDialog from '@/components/Map/DeviceDetailDialog/LampDialog.vue'
import MonitorDialog from '@/components/Map/DeviceDetailDialog/MonitorDialog.vue'
import SoilDialog from '@/components/Map/DeviceDetailDialog/SoilDialog.vue'
import { MapCore } from '@/components/Map/mapCore.ts'
import { deviceTypeMap } from '@/constants'
import { useOn } from '@/hooks/useOn.ts'
import { useAppStore } from '@/store/app.ts'
import { emitter } from '@/utils/events'

let map: L.Map
const mapCore = new MapCore()

/** S 标绘地图设备 */
const imgMap: Record<DeviceType, any> = {
  1: img1,
  2: img2,
  3: img3,
  4: img4,
}

const iconMap: Partial<Record<DeviceType, L.Icon>> = {}
for (const type of Object.keys(deviceTypeMap)) {
  iconMap[type] = L.icon({
    iconUrl: imgMap[type],
    iconSize: [37.6, 73.6],
    iconAnchor: [19.2, 73.6],
  })
}

const imgOfflineMap: Record<DeviceType, any> = {
  1: imgOffline1,
  2: imgOffline2,
  3: imgOffline3,
  4: imgOffline4,
}
const iconOfflineMap: Partial<Record<DeviceType, L.Icon>> = {}
for (const type of Object.keys(deviceTypeMap)) {
  iconOfflineMap[type] = L.icon({
    iconUrl: imgOfflineMap[type],
    iconSize: [37.6, 73.6],
    iconAnchor: [19.2, 73.6],
  })
}

/** 设备弹窗 */
const dialogComp: Record<DeviceType, any> = {
  1: markRaw(LampDialog),
  2: markRaw(SoilDialog),
  3: markRaw(InsectDialog),
  4: markRaw(MonitorDialog),
}

let crs: CRSTypes

const deviceList = ref<Device[]>([])
let deviceMarkerList: L.Marker[] = []
const deviceLoading = ref(false)
const dialogVisible = ref(false)
const selectedDevice = ref<Device>()
const deviceType = ref<DeviceType>()
const hasInitializedMapView = ref(false)
function queryDevice(deviceType: DeviceType) {
  clearDeviceMarker()
  deviceLoading.value = true
  CommonAPI.deviceList({ deviceType }).then((res) => {
    deviceList.value = res
    for (const item of deviceList.value) {
      const coord = gcoord.transform([item.point.longitude, item.point.latitude], gcoord.WGS84, crs)
      item.point.latitude = coord[1]
      item.point.longitude = coord[0]
      const { latitude: lat, longitude: lng } = item.point

      const icon = item.status ? iconMap[item.deviceType] : iconOfflineMap[item.deviceType]
      const marker = L.marker({ lat, lng }, { icon })
      marker.addTo(map)
      marker.data = item
      deviceMarkerList.push(marker)

      /** 点击展示设备弹窗 */
      marker.on('click', (e) => {
        selectedDevice.value = item
        dialogVisible.value = true
      })
    }

    if (deviceList.value.length) {
      hasInitializedMapView.value = true
      const { latitude: lat, longitude: lng } = deviceList.value[0].point
      map.setView({ lat, lng }, 15, { animate: false })
      map.zoomIn()
    }
    else {
      // 检查是否需要设置地图初始视图
      !farmlandLoading.value && trySetInitialMapView()
    }
  }).finally(() => {
    deviceLoading.value = false
  })
}

function clearDeviceMarker() {
  for (const marker of deviceMarkerList) {
    marker.remove()
  }

  deviceMarkerList = []
  // 清除选中的设备marker引用
  selectedDeviceMarker.value = null
}

/** E 标绘地图设备 */

/** S 标绘地块 */
const colorList = ['#FFC72F', '#FF691F', '#20F6E9', '#21EBAB', '#1F98FF', '#CE54EC', '#295BFF', '#D9F421', '#44F421', '#72D2FF']
const farmlandList = ref<Farmland[]>([])
let farmlandMarkerList: L.Marker[] = []
let farmlandPolygonList: L.Polygon[] = []
const farmlandLoading = ref(false)
// 选中地块
const selectedMarker = ref<L.Marker>(null)
const selectedEl = ref<Element>(null)
function queryFarmland(showCenterMarker: boolean) {
  farmlandLoading.value = true
  clearFarmland()
  CommonAPI.farmlandList().then((res) => {
    farmlandList.value = res
    appStore.farmlandList = res
    for (const [index, item] of farmlandList.value.entries()) {
      // 绘制地块多边形
      const lagLngs = item.range.coordinates.map((item) => {
        const coord = gcoord.transform([item.longitude, item.latitude], gcoord.BD09, crs)
        item.latitude = coord[1]
        item.longitude = coord[0]

        return { lat: item.latitude, lng: item.longitude }
      })
      const color = colorList[index % colorList.length]
      const polygon = L.polygon(lagLngs, { color, weight: 1, fillOpacity: 0.12 }).addTo(map)
      polygon.data = item
      farmlandPolygonList.push(polygon)

      // 绘制多边形中心marker
      const center = polygon.getBounds().getCenter()
      const marker = drawFarmlandMarker(center, item)
      // 只有总览页面才需要全部展示marker，其他页面点击时才展示
      if (showCenterMarker) {
        marker.addTo(map)
      }

      // 点击marker添加选中/取消选中
      polygon.on('click', () => {
        onFarmlandClick(marker, item)
      })
    }

    if (farmlandList.value.length && !hasInitializedMapView.value && !deviceLoading.value) {
      trySetInitialMapView()
    }
  }).finally(() => {
    farmlandLoading.value = false
  })
}

/**
 * 尝试设置地图初始视图
 * 当设备和地块数据都加载完成且没有设备时，定位到第一个地块中心点
 */
function trySetInitialMapView() {
  hasInitializedMapView.value = true
  const center = farmlandPolygonList[0].getCenter()
  map.setView(center, 15, { animate: false })
  map.zoomIn()
}

/**
 * 绘制地块中心marker
 */
function drawFarmlandMarker(latLng: L.LatLngExpression, data: Farmland) {
  // 绘制地块中心marker
  const content = `
        <div class="markerTooltip w-fit -translate-x-1/2 -translate-y-full mt-13PX pointer-events-none">
          <section class="mainContent use-bg w-195PX h-98PX px-16PX pt-3Px pointer-events-auto">
            <header class="header font-youShe h-35PX text-20PX text-#18FFC6 tracking-1PX truncate">${data.name}</header>
            <section class="mt-5PX" text="14PX #D0EBDD">
              <div class="truncate">
                <span>种植作物：</span>
                <span title="${data.cropNames ?? '无'}">${data.cropNames ?? '无'}</span>
              </div>
              <div class="truncate">
                <span>地块面积：</span>
                <span>${data.mu ? `${data.mu}亩` : '无'}</span>
              </div>
            </section>
          </section>
          <footer class="footer use-bg w-43.2PX h-73.6Px mx-auto mt-5PX pointer-events-auto"></footer>
        </div>
                `
  const icon = L.divIcon({
    html: content,
    iconSize: [0, 0],
  })
  const marker = L.marker(latLng, { icon, riseOnHover: true })
  // 给marker携带自定义数据
  marker.data = data
  farmlandMarkerList.push(marker)

  marker.on('click', () => {
    onFarmlandClick(marker, data)
  })

  return marker
}

const appStore = useAppStore()

function onFarmlandClick(marker: L.Marker, data: Farmland) {
  !map.hasLayer(marker) && marker.addTo(map)
  const el = marker.getElement().querySelector('.markerTooltip')
  if (selectedEl.value) {
    selectedEl.value.classList.remove('active')
    selectedMarker.value.setZIndexOffset(0)
    deviceType.value && selectedMarker.value.remove()
  }

  // 重复点击取消选中
  if (appStore.farmlandId === data.id) {
    appStore.farmlandId = null
    emitter.emit('farmlandClick', null)
    selectedEl.value = null

    // 如果deviceType为空(总览页)，重新显示所有地块marker
    if (!deviceType.value) {
      showAllFarmlandMarkers()
    }

    return
  }

  appStore.farmlandId = data.id
  selectedEl.value = el
  selectedMarker.value = marker
  el.classList.add('active')
  // 将当前marker放在顶层
  marker.setZIndexOffset(999999)
  // 将地图定位到当前位置
  const bounds = data.range.coordinates.map(item => [item.latitude, item.longitude]) as any
  map.fitBounds(bounds, { maxZoom: 18 })

  emitter.emit('farmlandClick', data.id)

  // 如果deviceType为空(总览页)，隐藏其他地块marker
  if (!deviceType.value) {
    hideOtherFarmlandMarkers(marker)
  }
}

function activeFarmlandMarker(marker: L.Marker) {
  !map.hasLayer(marker) && marker.addTo(map)
  const el = marker.getElement().querySelector('.markerTooltip')
  if (selectedEl.value) {
    selectedEl.value.classList.remove('active')
    selectedMarker.value.setZIndexOffset(0)
    deviceType.value && selectedMarker.value.remove()
  }

  selectedEl.value = el
  selectedMarker.value = marker
  el.classList.add('active')
  // 将当前marker放在顶层
  marker.setZIndexOffset(999999)
  // 将地图定位到当前位置
  const bounds = marker.data.range.coordinates.map(item => [item.latitude, item.longitude])
  map.fitBounds(bounds, { maxZoom: 18 })

  // 如果deviceType为空，隐藏其他地块marker
  if (!deviceType.value) {
    hideOtherFarmlandMarkers(marker)
  }
}

/**
 * 隐藏除指定marker外的其他地块marker
 */
function hideOtherFarmlandMarkers(selectedMarker: L.Marker) {
  for (const marker of farmlandMarkerList) {
    if (marker !== selectedMarker) {
      marker.remove()
    }
  }
}

/**
 * 显示所有地块marker
 */
function showAllFarmlandMarkers() {
  for (const marker of farmlandMarkerList) {
    if (!map.hasLayer(marker)) {
      marker.addTo(map)
    }
  }
}

function clearFarmland() {
  for (const polygon of farmlandPolygonList) {
    polygon.remove()
  }

  farmlandPolygonList = []
  for (const marker of farmlandMarkerList) {
    marker.remove()
  }

  farmlandMarkerList = []
}

useOn(emitter, 'cancelFarmlandSelect', () => {
  if (selectedEl.value) {
    appStore.farmlandId = null
    selectedEl.value.classList.remove('active')
    selectedMarker.value.setZIndexOffset(0)
    selectedEl.value = null
    selectedMarker.value = null

    // 如果deviceType为空，重新显示所有地块marker
    if (!deviceType.value) {
      showAllFarmlandMarkers()
    }
  }
})

emitter.on('selectFarmlandById', (id) => {
  if (id) {
    const marker = farmlandMarkerList.find(item => item.data.id === id)
    activeFarmlandMarker(marker)
  }
  else {
    deviceType.value && selectedMarker.value.remove()
    emitter.emit('farmlandClick', null)
    emitter.emit('cancelFarmlandSelect')
  }
})
/** E 标绘地块 */

/** S 监听切换设备 */
const selectedDeviceMarker = ref<L.Marker>()
watch(() => appStore.deviceId, (deviceId) => {
  // 清除之前选中设备的效果
  if (selectedDeviceMarker.value) {
    selectedDeviceMarker.value.setZIndexOffset(0)
    selectedDeviceMarker.value.getElement().classList.remove('selected-device-icon')
  }

  if (deviceId) {
    const marker = deviceMarkerList.find(item => item.data.id === deviceId)
    if (marker) {
      marker.setZIndexOffset(999999)
      marker.getElement().classList.add('selected-device-icon')
      selectedDeviceMarker.value = marker
      map.flyTo(marker.getLatLng())
    }
    else {
      // ElMessage.info({ message: '该设备没有定位', grouping: true })
    }
  }
  else {
    selectedDeviceMarker.value = null
  }
})
/** E 监听切换设备 */

/** S 切换页面 */
const route = useRoute()
watch(route, () => {
  deviceType.value = route.meta.deviceType
  hasInitializedMapView.value = false // 重置地图视图初始化状态
  queryDevice(deviceType.value)
  queryFarmland(!deviceType.value)
}, { immediate: true })
/** E 切换页面 */

onMounted(() => {
  map = mapCore.init('map')
  crs = map.options.crs === L.CRS.EPSG3857 ? gcoord.GCJ02 : gcoord.BD09
})

onUnmounted(() => {
  map.remove()
})
</script>

<template>
  <div>
    <div id="map" v-loading="deviceLoading || farmlandLoading" class="size-screen fixed inset-0" />
    <template v-if="dialogVisible">
      <MonitorDialog v-if="selectedDevice.deviceType == 4" v-model="dialogVisible" :data="selectedDevice.misc" />
      <component :is="dialogComp[selectedDevice.deviceType]" v-else v-model="dialogVisible" :deviceId="selectedDevice?.id" :serial="selectedDevice.name" />
    </template>
  </div>
</template>

<style scoped lang="scss">
#map {
  background-color: transparent;

  &::before {
    position: absolute;
    inset: 0;
    z-index: 999;
    pointer-events: none;
    content: '';
    background: url(@/assets/layout/mask.webp) center / 100% 100% no-repeat;
    animation: fade 1s;

    @keyframes fade {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }
  }

  &::after {
    position: absolute;
    inset: 0;
    z-index: 999;
    pointer-events: none;
    content: '';
    background: url(@/assets/layout/mask.webp) center / 100% 100% no-repeat;
    animation: fade 1s;

    @keyframes fade {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }
  }
}

// 清除tooltip默认样式
:deep(.leaflet-tooltip-pane) {
  .leaflet-tooltip {
    color: unset;
    // padding: unset;
    background-color: unset;
    border: unset;
    box-shadow: unset;

    &::before {
      display: none;
    }
  }
}
</style>

<style lang="scss">
.markerTooltip {
  .mainContent {
    background-image: url(@/assets/map/tooltip-bg.webp);
    transition: 0.3s all;
  }

  .footer {
    background-image: url(@/assets/map/marker-bottom.webp);
    transition: 0.3s all;
  }

  // 选中样式
  &.active {
    .mainContent {
      color: #f9f7f0;
      background-image: url(@/assets/map/tooltip-bg-active.webp);

      .header {
        color: #ffea00;
      }
    }

    .footer {
      background-image: url(@/assets/map/marker-bottom-active.webp);
    }
  }
}

// 选中设备图标样式
.selected-device-icon {
  position: relative;
  filter: drop-shadow(0 0 10px rgba(255, 234, 0, 0.8)) drop-shadow(0 0 20px rgba(255, 234, 0, 0.4));
  animation: pulse-glow-bounce 2s infinite;
}

@keyframes pulse-glow-bounce {
  0%,
  100% {
    top: 0;
    filter: drop-shadow(0 0 10px rgba(255, 234, 0, 0.8)) drop-shadow(0 0 20px rgba(255, 234, 0, 0.4));
  }

  25% {
    top: -3px;
    filter: drop-shadow(0 0 12px rgba(255, 234, 0, 0.9)) drop-shadow(0 0 25px rgba(255, 234, 0, 0.5));
  }

  50% {
    top: -6px;
    filter: drop-shadow(0 0 15px rgba(255, 234, 0, 1)) drop-shadow(0 0 30px rgba(255, 234, 0, 0.6));
  }

  75% {
    top: -3px;
    filter: drop-shadow(0 0 12px rgba(255, 234, 0, 0.9)) drop-shadow(0 0 25px rgba(255, 234, 0, 0.5));
  }
}
</style>
