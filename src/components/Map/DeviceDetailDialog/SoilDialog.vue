<script setup lang="ts">
import type { EChartsOption } from '#/index'
import type { SoilDetail } from '@/api/common/type'
import type { LineChartData } from '@/api/lamp/type'
import type { SMDParamConfig } from '@/api/soil/type'
import type Chart from '@/components/Chart/Chart.vue'
import type { DateType } from '@/constants'
import dayjs from 'dayjs'
import { CommonAPI } from '@/api/common/common.ts'
import { SoilAPI } from '@/api/soil/soil.ts'
import npk from '@/assets/home/<USER>/<EMAIL>'
import nitrogen from '@/assets/home/<USER>/icon_danlizhi.webp'
import air_rh from '@/assets/home/<USER>/<EMAIL>'
import air_temp from '@/assets/home/<USER>/<EMAIL>'
import conductivity from '@/assets/home/<USER>/<EMAIL>'
import batteryLevel from '@/assets/home/<USER>/<EMAIL>'
import batteryVoltage from '@/assets/home/<USER>/<EMAIL>'
import rainfall from '@/assets/home/<USER>/<EMAIL>'
import co2 from '@/assets/home/<USER>/<EMAIL>'
import wind_speed from '@/assets/home/<USER>/<EMAIL>'
import wind_direction from '@/assets/home/<USER>/<EMAIL>'
import radiation_accumulated from '@/assets/home/<USER>/<EMAIL>'
import photosynthesis from '@/assets/home/<USER>/<EMAIL>'
import kalium from '@/assets/home/<USER>/<EMAIL>'
import phosphorus from '@/assets/home/<USER>/icon_linlizhi.webp'
import ph from '@/assets/home/<USER>/<EMAIL>'
import pm2_5 from '@/assets/home/<USER>/<EMAIL>'
import pm10 from '@/assets/home/<USER>/<EMAIL>'
import sunshine_hours from '@/assets/home/<USER>/<EMAIL>'
import pressure from '@/assets/home/<USER>/<EMAIL>'
import moisture from '@/assets/home/<USER>/<EMAIL>'
import temperature from '@/assets/home/<USER>/<EMAIL>'
import salt from '@/assets/home/<USER>/<EMAIL>'
import water_level from '@/assets/home/<USER>/<EMAIL>'
import rainfall_accumulated from '@/assets/home/<USER>/<EMAIL>'
import illuminance from '@/assets/home/<USER>/<EMAIL>'
import evaporation from '@/assets/home/<USER>/<EMAIL>'
import uv_index from '@/assets/home/<USER>/<EMAIL>'
import solar_radiation from '@/assets/home/<USER>/<EMAIL>'
import { dateTypeMap } from '@/constants'
import { useAppStore } from '@/store/app.ts'
import { formateMinutes } from '@/utils'

const { deviceId, serial } = defineProps<{ deviceId: number, serial: string }>()

const appStore = useAppStore()
const ratio = computed(() => appStore.ratio)

const iconMap = {
  temperature,
  moisture,
  salt,
  conductivity,
  ph,
  nitrogen,
  phosphorus,
  kalium,
  npk,
  batteryLevel,
  batteryVoltage,
  air_temp,
  air_rh,
  pressure,
  illuminance,
  co2,
  pm2_5,
  pm10,
  wind_speed,
  wind_direction,
  solar_radiation,
  radiation_accumulated,
  rainfall,
  rainfall_accumulated,
  evaporation,
  sunshine_hours,
  photosynthesis,
  water_level,
  uv_index,
}

const dialogVisible = defineModel<boolean>()

/** S基础信息展示 */
const baseInfo = ref<SoilDetail>({})
const baseInfoLoading = ref(false)
function queryBaseInfo() {
  baseInfoLoading.value = true
  CommonAPI.deviceDetail<SoilDetail>(deviceId, 2).then((res) => {
    baseInfo.value = res
  }).finally(() => {
    baseInfoLoading.value = false
  })
}

queryBaseInfo()

const parameterList = ref<SMDParamConfig[]>()
const parameterListLoading = ref(false)
function queryParamList() {
  parameterListLoading.value = true

  SoilAPI.parameterList(deviceId).then((res) => {
    parameterList.value = []
    chartList.value = []
    for (const item of res) {
      if (item.enabled) {
        parameterList.value.push(item)
        if (item.chart) {
          chartList.value.push(item)
        }
      }
    }

    if (chartList.value.length) {
      currentChart.value = chartList.value[0]
      queryChartData()
    }
  }).finally(() => {
    parameterListLoading.value = false
  })
}

queryParamList()

const chartList = ref([])

/** E基础信息展示 */

/** S切换按钮 */
const currentChart = ref<SMDParamConfig>({})
function changeChart(row: SMDParamConfig) {
  currentChart.value = row
  queryChartData()
}

const chartData = ref<LineChartData>({})
const chartDataLoading = ref(false)
function queryChartData() {
  chartDataLoading.value = true
  SoilAPI.chart(dateType.value, deviceId, currentChart.value.paramCode, { query: date.value }).then((res) => {
    chartData.value = res || {}
  }).finally(() => {
    chartDataLoading.value = false
  })
}

const chartRef = ref<InstanceType<typeof Chart>>()
/**
 * 温湿度的x轴一致，直接用chartData1的x轴即可
 */
const xData = computed(() => chartData.value.values?.map(item => item.x + chartData.value.xunit) || [])

/**
 * 切换时间类型
 */
const dateType = ref<DateType>('day')
function changeDateType(type: DateType) {
  dateType.value = type
}

function dateChange() {
  queryChartData()
}

const date = ref(dayjs().format('YYYY-MM-DD'))
/** E切换按钮 */

/** S 折线图 */
const option = computed(() => {
  return {
    grid: {
      top: '20%',
      bottom: '0%',
      left: '8%',
      right: '12%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        for (const [index, param] of params.entries()) {
          // 为第一个系列使用绿色，第二个系列使用蓝色
          const color = index === 0 ? '#29FFBB' : '#0194F1'
          result += `
          <div class="flex justify-between gap-x-30">
            <div>
              <span class="inline-block w-10Px h-10Px rounded-10Px mr-5Px" style="background-color:${color};"></span>
              <span>${param.seriesName}</span>
            </div>
            <span>${param.value}</span>
          </div>`
        }

        return result
      },
    },
    dataZoom: [{ type: 'inside' }],
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        symbol: ['none', 'arrow'],
        symbolOffset: [0, 10],
        lineStyle: {
          color: 'rgba(55,255,191,0.2)',
        },
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
        fontSize: 12 * ratio.value,
      },
      // 不从0开始
      boundaryGap: true,
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
          fontSize: 12 * ratio.value,
        },
        name: chartData.value.yunit && `单位:${chartData.value.yunit}`,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        minInterval: 1,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
    ],
    legend: {
      show: false,
    },
    series: [
      {
        name: chartData.value.chartName,
        data: chartData.value.values?.map(item => item.y),
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          shadowColor: '#29FFBB',
          shadowBlur: 7,
          color: '#062129',
          borderColor: '#29FFBB',
          borderWidth: 2,
        },
        lineStyle: {
          color: '#29FFBB',
        },
        yAxisIndex: 0,
        areaStyle: {
          color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: 'rgba(64,241,169,0.3)' }, { offset: 1, color: 'rgba(64,241,169,0.1)' }] },
        },
      },
    ],
  } as EChartsOption
})
/** E 折线图 */
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="soilDialog">
    <DialogTitle title="墒情气象一体机" class="mt-10" @close="dialogVisible = false" />

    <section class="flex mt-26">
      <aside v-loading="parameterListLoading || baseInfoLoading" class="w-450 shrink-0" b="r-1 solid #08593D">
        <header class="mt-36 pl-20 py-2" text="16 #DAF4EC">
          <span>设备编号：</span>
          <span>{{ serial }}</span>
        </header>
        <header class="leftHeader mt-39 ml-20 mr-24 flex items-center justify-between px-26" text="14" b="1 solid">
          <div>
            <span text="#A5C8BD">设备状态：</span>
            <span text="#EDF8F3">{{ baseInfo.online ? '在线' : '离线' }}</span>
          </div>
          <div>
            <span text="#A5C8BD">本次开机时长：</span>
            <span text="#EDF8F3">{{ formateMinutes(baseInfo.runningTime) }}</span>
          </div>
        </header>

        <el-scrollbar class="pl-20 pr-24 mt-12" height="16.78vw">
          <section class="gap-20" grid="~ cols-2">
            <div v-for="item in parameterList" :key="item.paramCode" class="item flex bg-#0B4233/18 h-90" b="1 solid #06AE59/18">
              <aside class="relative shrink-0 w-90 h-90">
                <img :src="iconMap[item.icon]" :alt="item.paramName" class="absolute left-1/2 top-1/2 -translate-1/2 w-128 h-128" draggable="false" />
              </aside>
              <div class="flex flex-col justify-center gap-11 -ml-7">
                <header class="title" text="14 #CEFFE6">{{ item.paramName }}</header>
                <footer v-if="baseInfo[item.columnName] != null" text="#CEFFE6">
                  <span class="value font-din font-bold" text="16">{{ baseInfo[item.columnName] }}</span>
                  <span class="unit" text="13">{{ item.unit }}</span>
                </footer>
                <div v-else>--</div>
              </div>
            </div>
          </section>
        </el-scrollbar>
      </aside>
      <aside class="grow overflow-hidden">
        <header class="flex items-center pl-29">
          <HorizontalScroll class="w-600">
            <div class="flex">
              <button
                v-for="item in chartList" :key="item.paramCode"
                class="menuBtn use-bg font-alimama min-w-139 h-56 pointer-events-auto tracking-3 pointer-events-auto -ml-10"
                :class="{ active: currentChart.paramCode === item.paramCode }"
                text="14 #E2FFF0/50"
                @click="changeChart(item)"
              >
                {{ item.paramName }}
              </button>
            </div>
          </HorizontalScroll>
        </header>

        <div class="flex items-center justify-between ml-20 mt-10 -mr-12">
          <div class="flex items-center">
            <button
              v-for="(label, value) in dateTypeMap" :key="value"
              class="button use-bg w-61 h-39 leading-23 tracking-2"
              :class="{ active: dateType === value }"
              text="12 #86A29D"
              @click="changeDateType(value)"
            >
              {{ label }}
            </button>
          </div>
          <SelectDate v-model="date" class="ml-auto mr-10" :type="dateType" @change="dateChange" />
        </div>
        <div v-loading="chartDataLoading " class="w-685 h-342">
          <Chart v-if="xData.length" ref="chartRef" class="w-745 h-full -ml-30 -mr-60" :option="option" :noMerge="true" />
          <div v-else class="empty" />
        </div>
      </aside>
    </section>
  </el-dialog>
</template>

<style scoped lang="scss">
.leftHeader {
  position: relative;
  height: 71px;
  border-image: linear-gradient(to bottom, rgba(13, 117, 89, 0.64), rgba(13, 117, 89, 0.2));
  border-image-slice: 0 1;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    background-color: rgba(13, 117, 89, 0.64);
  }
}

.item {
  .value,
  .unit {
    background: linear-gradient(0deg, #00d851 0%, #fff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.menuBtn {
  background-image: url(@/assets/layout/menu-bg.webp);
  transition: all 0.3s;

  &.active {
    color: #e2fff0;
    // 文本发光
    text-shadow: 0 0 10px #e2fff0;
    background-image: url(@/assets/layout/menu-bg-active.webp);
  }
}

.button {
  transition: 0.3s all;

  &.active {
    color: #ceffe6;
    background-image: url(@/assets/common/radio-bg.webp);
  }
}
</style>

<style lang="scss">
.soilDialog.soilDialog {
  --el-dialog-width: 1172px;

  height: 595px;
  padding: 25px;
  background: url(@/assets/common/dialog-bg.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
