<script setup lang="ts">
import type { EChartsOption } from '#/index'
import type { InsetDetail } from '@/api/common/type'
import type { AnalyzeL<PERSON>, LineChart, PieVO } from '@/api/insect/type'
import type { DateType2 } from '@/constants'
import dayjs from 'dayjs'
import { CommonAPI } from '@/api/common/common.ts'
import { InsectAPI } from '@/api/insect/insect.ts'
import { dateTypeMap2 } from '@/constants'

import { getState } from '@/hooks'
import { formateMinutes } from '@/utils'

const { deviceId, serial } = defineProps<{ deviceId: number, serial: string }>()

const dialogVisible = defineModel<boolean>()

/** S基础信息展示 */
const baseInfo = ref<InsetDetail>({})
const baseInfoLoading = ref(false)
function queryBaseInfo() {
  baseInfoLoading.value = true
  CommonAPI.deviceDetail<InsetDetail>(deviceId, 3).then((res) => {
    baseInfo.value = res
  }).finally(() => {
    baseInfoLoading.value = false
  })
}

queryBaseInfo()

const normalMode = computed(() => !baseInfo.value.controlConfig?.sleepMode?.enabled)

/** E基础信息展示 */

// 格式化存储空间显示
function formatStorage(usedSpace?: number, totalSpace?: number) {
  if (!totalSpace)
    return '--'
  return `${Math.round(usedSpace / 1024 / 1024 / 1024)}/${Math.round(totalSpace / 1024 / 1024 / 1024)}GB`
}

// 格式化存储空间显示 传入字节数 返回kb/mb/gb
function formatStorage2(bytes: number) {
  if (!bytes || bytes < 0) {
    return '0B'
  }

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  // 如果是字节，不显示小数
  if (unitIndex === 0) {
    return `${size}${units[unitIndex]}`
  }

  // 其他单位保留1位小数
  return `${size.toFixed(1)}${units[unitIndex]}`
}

/** S切换按钮 */
const date = ref([dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD HH:mm:ss')])
const dateType = ref<DateType2>('day')
const chartDataLoading = ref(false)
function queryChartData() {
  chartDataLoading.value = true
  InsectAPI.chart(deviceId, dateType.value, date.value[0], date.value[1]).then((res) => {
    lineChartData.value = res?.lineCharts || []
    pieChartData.value = res?.pie || []
  }).finally(() => {
    chartDataLoading.value = false
  })
}

/**
 * 切换时间类型
 */
function changeDateType(type: DateType2) {
  dateType.value = type
}

function dateChange() {
  if (dateType.value === 'day') {
    handleQuery()
    lineChartData.value = []
    pieChartData.value = []
  }
  else {
    tableList.value = []
    queryChartData()
  }
}

/** S 日面板 分页查询分析记录 */
const state = getState<AnalyzeLog>()
const { tableList, queryParams, total, loading } = toRefs(state)

queryParams.value.pageSize = 2
function handleQuery() {
  loading.value = true
  queryParams.value.startTime = date.value[0]
  queryParams.value.endTime = date.value[1]
  queryParams.value.deviceId = deviceId
  InsectAPI.analyzeLog(queryParams.value).then((res) => {
    tableList.value = res.records
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()
/** E 日面板 分页查询分析记录 */

/** E切换按钮 */

/** S 折线图 */
const lineChartData = ref<LineChart[]>([])
const lineOption = computed(() => {
  const series: EChartsOption['series'] = lineChartData.value.map((item) => {
    return {
      name: item.label,
      data: item.line.map(el => [el.x, el.y]),
      type: 'line',
      symbol: 'circle',
      symbolSize: 8,
      yAxisIndex: 0,
    }
  })
  return {
    grid: {
      top: '20%',
      bottom: '0%',
      left: '8%',
      right: '8%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    dataZoom: [{ type: 'inside' }],
    xAxis: {
      type: 'category',
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        symbol: ['none', 'arrow'],
        symbolOffset: [0, 10],
        lineStyle: {
          color: 'rgba(55,255,191,0.2)',
        },
      },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
      },
      // 不从0开始
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
        },
        name: '单位：只',
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        minInterval: 1,
        axisLine: {
          show: true,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
    ],
    legend: {
      show: false,
    },
    series,
  } as EChartsOption
})
/** E 折线图 */

/** S 饼图 */
const pieChartData = ref<PieVO[]>([])
const pieOption = computed(() => {
  return {
    title: {
      text: '病虫数量',
      left: '49%',
      top: '44%',
      textAlign: 'center',
      textStyle: {
        color: '#BDD0F7',
        fontSize: 16,
      },
      subtext: pieChartData.value.reduce((prev, cur) => prev + cur.value, 0).toString(),
      subtextStyle: {
        color: '#e1ebe6',
        textAlign: 'center',
        fontSize: 16,
      },
    },
    legend: {
      show: false,
      bottom: '5%',
      left: 'center',
      icon: 'rect',
      itemWidth: 16,
      itemHeight: 8,
      itemGap: 13,
      textStyle: {
        color: '#BDD0F7',
      },
    },
    color: ['#FFCD34', '#0099F9', '#51C4B2', '#35D04F'],
    tooltip: {
      trigger: 'item',
      show: true,
    },
    series: [
      {
        name: '虫数量占比',
        type: 'pie',
        radius: ['43%', '58%'],
        center: ['50%', '50%'],
        // roseType: 'area',
        // 饼图间隙，从echarts5.5.0开始支持
        // padAngle: 4,
        itemStyle: {
          borderRadius: 2,
        },
        data: pieChartData.value,
        label: {
          show: false,
          color: '#BDD0F7',
          formatter: '{c}次 {d}%',
          padding: [-15, -70],
        },
        labelLine: {
          show: false,
        },
        labelLayout: {
          verticalAlign: 'bottom',
          dy: -10,
          // moveOverlap: 'shiftY',
        },
      },
    ],
  } as EChartsOption
})
/** E 饼图 */
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="insectDialog">
    <DialogTitle title="虫情分析仪" class="mt-10" @close="dialogVisible = false" />

    <section class="flex mt-26">
      <aside v-loading="baseInfoLoading" class="w-480 shrink-0" b="r-1 solid #08593D">
        <header class="mt-36 pl-20 py-2" text="16 #DAF4EC">
          <span>设备编号：</span>
          <span>{{ serial }}</span>
        </header>

        <section class="flex gap-x-29 mt-45 ml-20">
          <img src="@/assets/map/<EMAIL>" alt="" class="w-96 h-255" />
          <div class="baseInfo w-291 h-290 p-30" b="1 solid">
            <div class="labelValue mb-21">
              <span class="label">设备状态：</span>
              <span class="value">{{ baseInfo.online ? '在线' : '离线' }}</span>
            </div>
            <div class="labelValue mb-21">
              <span class="label">存储空间：</span>
              <span class="value">{{ formatStorage(baseInfo.usedSpace, baseInfo.totalSpace) }}</span>
            </div>
            <div class="labelValue mb-21">
              <span class="label">累计拍照次数：</span>
              <span class="value">{{ baseInfo.snapCount }}</span>
            </div>
            <div class="labelValue mb-21">
              <span class="label">本次运行时间：</span>
              <span v-if="baseInfo.uptime" class="value">{{ formateMinutes(baseInfo.uptime) }}</span>
            </div>
            <div class="labelValue mb-21">
              <span class="label">手机电量：</span>
              <span class="value">{{ baseInfo.batteryLevel }}</span>
            </div>
            <div class="labelValue mb-21">
              <span class="label">最近拍照时间：</span>
              <span class="value">{{ baseInfo.lastSnapTime }}</span>
            </div>
            <div class="labelValue">
              <span class="label">数据更新时间：</span>
              <span class="value">{{ baseInfo.time }}</span>
            </div>
          </div>
        </section>

        <header class="ml-20" text="14 #A5C8BD"> 当前运行模式： </header>

        <footer class="ml-20 mt-17" text="14">
          <div class="flex flex-col justify-between w-419 h-72 p-17" b="1 solid #06AE59/18" :class="normalMode ? 'text-#48FFA2' : 'text-#C6D0CB'">
            <header>
              <SvgIcon :name="normalMode ? 'active' : 'not-active' " />
              <span class="ml-4">正常模式</span>
            </header>
            <div class="flex justify-between">
              <span>起始时间：{{ baseInfo.controlConfig?.normalMode?.startTime || '--' }}</span>
              <span>截止时间：{{ baseInfo.controlConfig?.normalMode?.endTime || '--' }}</span>
              <span>拍照间隔：{{ baseInfo.controlConfig?.normalMode?.interval || '--' }} 秒</span>
            </div>
          </div>
          <div class="mt-16 flex flex-col justify-between w-419 h-72 p-17" b="1 solid #06AE59/18" :class="!normalMode ? 'text-#48FFA2' : 'text-#C6D0CB'">
            <header>
              <SvgIcon :name="!normalMode ? 'active' : 'not-active' " />
              <span class="ml-4">休眠模式</span>
            </header>
            <div class="flex justify-between">
              <span>起始日期：{{ baseInfo.controlConfig?.sleepMode?.startDate || '--' }}</span>
              <span>截止日期：{{ baseInfo.controlConfig?.sleepMode?.endDate || '--' }}</span>
              <span>拍照时间：{{ baseInfo.controlConfig?.sleepMode?.photoTime || '--' }}</span>
            </div>
          </div>
        </footer>
      </aside>
      <aside class="grow overflow-hidden relative">
        <div class="flex items-center justify-between ml-20 mt-10 -mr-12">
          <div class="flex items-center">
            <button
              v-for="(label, value) in dateTypeMap2" :key="value"
              class="button use-bg w-61 h-39 leading-23 tracking-2"
              :class="{ active: dateType === value }"
              text="12 #86A29D"
              @click="changeDateType(value)"
            >
              {{ label }}
            </button>
          </div>
          <SelectDateRange v-model="date" class="ml-auto mr-10" :type="dateType" @change="dateChange" />
        </div>
        <div v-if="dateType === 'day'" v-loading="loading" class="empty !h-728 overflow-hidden">
          <template v-for="item in tableList" :key="item.deviceId">
            <header class="header ml-20 pl-20 mt-20 h-42 leading-42" text="16 #E1EBE6">采样时间：{{ item.time }}</header>
            <section class="mx-20 mt-17 flex gap-x-22">
              <div class="relative w-270 h-194" b="1 solid #1F8932">
                <Image :src="item.rawImage.raw" class="size-full" :preview-src-list="[item.rawImage.raw]" />
                <footer class="absolute bottom-0 left-0 right-0 h-28 leading-28 bg-#0D7559 pointer-events-none" text="center">采样图片</footer>
              </div>
              <div class="relative w-270 h-194" b="1 solid #1F8932">
                <Image :src="item.analyzeImage?.raw" class="size-full" :preview-src-list="[item.analyzeImage?.raw]" />
                <footer class="absolute bottom-0 left-0 right-0 h-28 leading-28 bg-#0D7559 pointer-events-none" text="center">分析图片</footer>
              </div>
            </section>
            <footer class="mx-20 mt-10 leading-24 pr-18" grid="~ cols-2">
              <div class="col-span-2 truncate">
                <span text="#A5C8BD">病虫名字：</span>
                <el-tooltip :content="item.analyzeResult?.map(item => `${item.name}  (${item.count})`).join('、')">
                  <span text="#EDF8F3">{{ item.analyzeResult?.map(item => `${item.name}  (${item.count})`).join('、') || '--' }}</span>
                </el-tooltip>
              </div>
              <div>
                <span text="#A5C8BD">病虫数量：</span>
                <span text="#EDF8F3">{{ item.insectTotal }}</span>
              </div>
              <div>
                <span text="#A5C8BD">照片大小：</span>
                <span text="#EDF8F3">{{ formatStorage2(item.analyzeImage?.size) }}</span>
              </div>
            </footer>
          </template>
          <div v-if="total" class="flex justify-end mt-10 absolute bottom-30 right-30">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              :total="total"
              size="small"
              :pager-count="5"
              :page-size="queryParams.pageSize"
              layout="total, prev, pager, next"
              @size-change="handleQuery"
              @current-change="handleQuery"
            />
          </div>
        </div>
        <template v-else>
          <header class="header ml-20 pl-20 mt-20 h-42 leading-42" text="16 #E1EBE6">虫数量统计</header>
          <div v-loading="chartDataLoading " class="w-685 h-302">
            <Chart v-if="lineChartData.length" class="w-745 h-full -ml-30 -mr-60" :option="lineOption" :noMerge="true" />
            <div v-else class="empty" />
          </div>
          <header class="header ml-20 pl-20 mt-20 h-42 leading-42" text="16 #E1EBE6">虫数量占比</header>
          <div v-loading="chartDataLoading " class="w-685 h-302">
            <Chart v-if="pieChartData.length" class="pieChart w-745 h-full -ml-30 -mr-60" :option="pieOption" />
            <div v-else class="empty" />
          </div>
        </template>
      </aside>
    </section>
  </el-dialog>
</template>

<style scoped lang="scss">
.baseInfo {
  position: relative;
  border-image: linear-gradient(to bottom, rgba(13, 117, 89, 0.64), transparent);
  border-image-slice: 0 1;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    background-color: rgba(13, 117, 89, 0.64);
  }
}

.labelValue {
  .label {
    color: #a5c8bd;
  }

  .value {
    color: #edf8f3;

    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}

.button {
  transition: 0.3s all;

  &.active {
    color: #ceffe6;
    background-image: url(@/assets/common/radio-bg.webp);
  }
}

.header {
  background: linear-gradient(to right, #0b4233 0%, rgba(11, 66, 51, 0.1) 100%);
}

.pieChart {
  position: relative;

  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 0;
    width: 309.4px;
    height: 301.6px;
    content: '';
    background: url(@/assets/map/<EMAIL>) center / 100% 100% no-repeat;
    transform: translate(-50%, -50%);
  }
}

:deep(.el-pager) {
  gap: 0 5px;
  margin: 0 5px;
}
</style>

<style lang="scss">
.insectDialog.insectDialog {
  --el-dialog-width: 1180px;

  height: 902px;
  padding: 25px;
  background: url(@/assets/common/bg_1180.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
