<script setup lang="ts">
import type { Misc } from '@/api/common/type'
import type { RecordItem } from '@/api/monitor/type'
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor/monitor.ts'

const { data } = defineProps<{ data: Misc }>()

const dialogVisible = defineModel<boolean>()

/** S 直播 */
const typeList = {
  1: '直播',
  2: '回放',
}

const current = ref(1)
function changeType(val: number) {
  current.value = val
}

const videoUrl = ref()
const monitorLoading = ref(false)
function queryMonitor() {
  monitorLoading.value = true
  MonitorAPI.play(data.deviceId, data.channelId)
    .then((res) => {
      videoUrl.value = res.wss_flv
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

queryMonitor()
/** E 直播 */

/** S 回放 */
const tableList = ref<RecordItem[]>([])
const timeRange = ref([dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')])
const loading = ref(false)
function queryRecord() {
  loading.value = true
  const [startTime, endTime] = timeRange.value
  MonitorAPI.deviceRecord(data.deviceId, data.channelId, startTime, endTime).then((res) => {
    tableList.value = res.recordList || []
  }).finally(() => {
    loading.value = false
  })
}

queryRecord()

const monitorDialog = ref(false)
const formData = ref<RecordItem>()
function detail(row: RecordItem) {
  formData.value = { ...row }
  monitorDialog.value = true
}
/** E 回放 */
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="monitorDialog">
    <DialogTitle :title="data.channelName" class="mt-10" @close="dialogVisible = false" />
    <div class="flex items-center ml-20 mt-10 -mr-12">
      <button
        v-for="(label, value) in typeList" :key="value"
        class="menuBtn use-bg font-alimama min-w-139 h-56 pointer-events-auto tracking-3 pointer-events-auto -ml-10"
        :class="{ active: current === Number(value) }"
        text="14 #E2FFF0/50"
        @click="changeType(Number(value))"
      >
        {{ label }}
      </button>
    </div>
    <transition name="el-zoom-in-center" mode="out-in">
      <div v-if="current == 1" class="flex justify-center mt-15">
        <Player :loading="monitorLoading" :videoUrl="videoUrl" :deviceId="data.deviceId" :channelId="data.channelId" class="w-918 h-500" />
      </div>
      <div v-else-if="current == 2">
        <div class="flex justify-end mb-10">
          <SelectDateTimeRange v-model="timeRange" @change="queryRecord" />
        </div>
        <el-table v-loading="loading" stripe :data="tableList" row-class-name="h-54" height="23vw" row-key="viewId">
          <el-table-column label="通道号" prop="deviceId" align="center" show-overflow-tooltip />
          <el-table-column label="通道名" prop="name" align="center" />
          <el-table-column label="开始时间 " prop="startTime" align="center" />
          <el-table-column label="结束时间" prop="endTime" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <div role="button" class="btn inline-block select-none rounded-4 cursor-pointer p-3" b="1 solid #07CB71" @click="detail(row)">
                <button class="btn-inner rounded-4 bg-#072317 cursor-pointer" text="14 #1DFF8C" b="1 solid #07CB71" p="x-9 y-3">播放视频</button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </transition>
    <PlayRecordDialog v-if="monitorDialog" v-model="monitorDialog" :deviceId="data.deviceId" :channelId="data.channelId" :start-time="formData.startTime" :end-time="formData.endTime" />
  </el-dialog>
</template>

<style scoped lang="scss">
.menuBtn {
  background-image: url(@/assets/layout/menu-bg.webp);
  transition: all 0.3s;

  &.active {
    color: #e2fff0;
    // 文本发光
    text-shadow: 0 0 10px #e2fff0;
    background-image: url(@/assets/layout/menu-bg-active.webp);
  }
}

.btn {
  background: rgba(7, 35, 23, 0.73);

  .btn-inner {
    box-shadow: 0 0 10px 0 rgba(7, 203, 113, 0.47) inset;
  }
}
</style>

<style lang="scss">
.monitorDialog.monitorDialog {
  --el-dialog-width: 1019px;

  height: 684px;
  padding: 25px;
  background: url(@/assets/common/dialog-bg.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
