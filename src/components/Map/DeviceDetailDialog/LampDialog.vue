<script setup lang="ts">
import type { EChartsOption } from '#/index'
import type { LampDetail } from '@/api/common/type'
import type { LineChartData } from '@/api/lamp/type'
import type Chart from '@/components/Chart/Chart.vue'
import type { DateType } from '@/constants'
import dayjs from 'dayjs'
import { CommonAPI } from '@/api/common/common.ts'
import { LampAPI } from '@/api/lamp/lamp.ts'
import { dateTypeMap } from '@/constants'
import { useAppStore } from '@/store/app.ts'

const { deviceId, serial } = defineProps<{ deviceId: number, serial: string }>()
const dialogVisible = defineModel<boolean>()

const isMB = computed(() => serial.startsWith('MB'))
const isMC = computed(() => serial.startsWith('MC'))
const appStore = useAppStore()
const ratio = computed(() => appStore.ratio)

/** S基础信息展示 */
const baseInfo = ref<LampDetail>({})
const baseInfoLoading = ref(false)
function queryBaseInfo() {
  baseInfoLoading.value = true
  CommonAPI.deviceDetail(deviceId, 1).then((res) => {
    baseInfo.value = res
  }).finally(() => {
    baseInfoLoading.value = false
  })
}

queryBaseInfo()

// 定位方式映射
const locateModeMap = {
  0: '无效定位',
  1: '卫星定位',
  2: '基站定位',
}

// 网络类型映射
const networkTypeMap = {
  1: '2G',
  2: '2.5G',
  3: '3G',
  4: '4G',
  5: '5G',
}

const baseInfoList = computed(() => {
  const { voltage, current, coordinate, batteryLevel, chargeW, lightCurrent, temperature, fanCurrent, humidity, time, locateMode, networkType, dump, light, controlConfig, solarPanelsCurrent } = baseInfo.value
  // 工作时段处理
  const workHours = controlConfig?.workHours?.map(item => `${item[0]}~${item[1]}`).join('  ') || null
  return [
    { id: 1, label: '电压：', value: voltage, unit: 'V' },
    { id: 2, label: '定位方式：', value: locateModeMap[locateMode], unit: null },
    { id: 3, label: '电流：', value: current, unit: 'A' },
    { id: 4, label: '实时定位：', value: coordinate && `${coordinate.latitude},${coordinate.longitude}`, unit: null },
    { id: 5, label: '电池电量：', value: batteryLevel, unit: '%' },
    { id: 6, label: '跌倒状态：', value: dump ? '跌倒' : '正常', unit: null, color: dump ? '#FF4D4F' : '#28F5B4' },
    { id: 7, label: '充电功率：', value: chargeW, unit: 'W' },
    { id: 8, label: '太阳能板电压：', value: solarPanelsCurrent, unit: 'V' },
    { id: 9, label: '灯状态：', value: light ? '开' : '关', unit: null, color: light ? '#28F5B4' : '' },
    { id: 10, label: '灯电流：', value: lightCurrent, unit: 'A' },
    { id: 11, label: '温度：', value: temperature, unit: '℃' },
    { id: 12, label: '风机电流：', value: fanCurrent, unit: 'A' },
    { id: 13, label: '湿度：', value: humidity, unit: '%' },
    { id: 14, label: '数据更新时间：', value: time, unit: null },
    { id: 15, label: '网络状态：', value: networkTypeMap[networkType], unit: null },
    { id: 16, label: '工作时段：', value: workHours, unit: null },
  ]
})
/** E基础信息展示 */

/** S切换按钮 */
const tabList = [
  { id: 1, title: '放电电流', fn: LampAPI.current },
  { id: 2, title: '充电电流', fn: LampAPI.chargeCurrent },
  { id: 3, title: '电压', fn: LampAPI.voltage },
  { id: 4, title: '亮灯统计', fn: LampAPI.lightCount },
  { id: 5, title: '杀虫数量', fn: LampAPI.killCount },
  { id: 6, title: '温湿度', fn: LampAPI.temperature },
]

/**
 * 切换数据类型
 */
const currentTab = ref(tabList[0])
function changeTab(row: typeof tabList[0]) {
  currentTab.value = row
  queryChartData()
}

const chartData1 = ref<LineChartData>({})
const chartDataLoading1 = ref(false)
function queryChartData() {
  chartDataLoading1.value = true
  currentTab.value.fn(dateType.value, deviceId, { query: date.value }).then((res) => {
    chartData1.value = res || {}
  }).finally(() => {
    chartDataLoading1.value = false
  })

  /** 温湿度还需额外请求湿度接口 */
  if (currentTab.value.id === 6) {
    queryHumidity()
  }
  else {
    chartData2.value = {}
  }
}

const chartData2 = ref<LineChartData>({})
const chartDataLoading2 = ref(false)

function queryHumidity() {
  chartDataLoading2.value = true
  LampAPI.humidity(dateType.value, deviceId, { query: date.value }).then((res) => {
    chartData2.value = res || {}
  }).finally(() => {
    chartDataLoading2.value = false
  })
}

const chartRef = ref<InstanceType<typeof Chart>>()
/**
 * 温湿度的x轴一致，直接用chartData1的x轴即可
 */
const xData = computed(() => chartData1.value.values?.map(item => item.x + chartData1.value.xunit) || [])

/**
 * 切换时间类型
 */
const dateType = ref<DateType>('day')
function changeDateType(type: DateType) {
  dateType.value = type
}

function dateChange() {
  queryChartData()
}

const date = ref(dayjs().format('YYYY-MM-DD'))
queryChartData()
/** E切换按钮 */

/** S 折线图 */
const option = computed(() => {
  return {
    grid: {
      top: '20%',
      bottom: '5%',
      left: '8%',
      right: '8%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        for (const [index, param] of params.entries()) {
          // 为第一个系列使用绿色，第二个系列使用蓝色
          const color = index === 0 ? '#29FFBB' : '#0194F1'
          // 亮灯统计时将0、1映射为关、开
          let displayValue = param.value
          if (currentTab.value.id === 4 && index === 0 && dateType.value === 'day') {
            displayValue = param.value === 0
              ? '关'
              : (param.value === 1
                  ? '开'
                  : param.value)
          }

          result += `
          <div class="flex justify-between">
            <div>
              <span class="inline-block w-10Px h-10Px rounded-10Px mr-5Px" style="background-color:${color};"></span>
              <span>${param.seriesName}：</span>
            </div>
            <span>${displayValue}</span>
          </div>`
        }

        return result
      },
    },
    dataZoom: [{ type: 'inside' }],
    xAxis: {
      type: 'category',
      data: xData.value,
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(56,89,130,0.18)',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: { show: false },
      axisLabel: {
        color: '#789590',
        align: 'center',
        margin: 4,
        fontSize: 12 * ratio.value,
      },
      // 不从0开始
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#789590',
          fontSize: 12 * ratio.value,
          formatter: (currentTab.value.id === 4) && (dateType.value === 'day')
            ? (value: number) => {
                // 亮灯统计时将0、1映射为关、开
                return value === 0
                  ? '关'
                  : (value === 1
                      ? '开'
                      : value.toString())
              }
            : undefined,
        },
        name: chartData1.value.yunit && (currentTab.value.id !== 4 || dateType.value !== 'day') ? `单位:${chartData1.value.yunit}` : undefined,
        nameLocation: 'end',
        nameRotate: 0,
        nameTextStyle: {
          color: '#789590',
          lineHeight: -10,
          verticalAlign: 'top',
          fontSize: 9,
        },
        z: 10,
        zlevel: 10,
        minInterval: 1,
        axisLine: {
          show: false,
          symbol: ['none', 'arrow'],
          symbolOffset: [0, 10],
          lineStyle: {
            color: 'rgba(55,255,191,0.2)',
          },
        },
      },
      currentTab.value.id === 6
        ? {
            type: 'value',
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#789590',
            },
            minInterval: 1,
            name: chartData2.value.yunit && `单位:${chartData2.value.yunit}`,
            nameLocation: 'end',
            nameRotate: 0,
            nameTextStyle: {
              color: '#789590',
              lineHeight: -10,
              verticalAlign: 'top',
              fontSize: 9,
            },
            z: 10,
            zlevel: 10,
            axisLine: { show: false },
          }
        : undefined,
    ].filter(Boolean),
    legend: {
      show: false,
    },
    series: [
      {
        name: chartData1.value.chartName,
        data: chartData1.value.values?.map(item => item.y),
        type: 'line',
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          shadowColor: '#29FFBB',
          shadowBlur: 7,
          color: '#062129',
          borderColor: '#29FFBB',
          borderWidth: 2,
        },
        lineStyle: {
          color: '#29FFBB',
        },
        yAxisIndex: 0,
        areaStyle: {
          color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: 'rgba(64,241,169,0.3)' }, { offset: 1, color: 'rgba(64,241,169,0.1)' }] },
        },
      },
      currentTab.value.id === 6
        ? {
            name: chartData2.value.chartName,
            data: chartData2.value.values?.map(item => item.y),
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              shadowColor: '#0194F1',
              shadowBlur: 7,
              color: '#062129',
              borderColor: '#0194F1',
              borderWidth: 2,
            },
            lineStyle: {
              color: '#0194F1',
            },
            yAxisIndex: 1,
            areaStyle: {
              color: { type: 'linear', x: 0, y: 0, x2: 0, y2: 1, colorStops: [{ offset: 0, color: 'rgba(1,148,241,0.3)' }, { offset: 1, color: 'rgba(1,148,241,0)' }] },
            },
          }
        : undefined,
    ].filter(Boolean),
  } as EChartsOption
})
/** E 折线图 */
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close align-center class="lampDialog">
    <DialogTitle title="智慧杀虫灯" class="mt-10" @close="dialogVisible = false" />

    <header class="mt-36 pl-20 py-2" text="16 #DAF4EC">
      <span>设备编号：</span>
      <span>{{ serial }}</span>
    </header>
    <header class="flex pl-62 mt-18 gap-x-81">
      <template v-if="!baseInfoLoading">
        <template v-if="!baseInfo.installImages?.length">
          <img v-if="isMC" src="@/assets/map/deviceDetail/scd2.png" alt="" class="w-102 h-213 pt-34" draggable="false" />
          <img v-else src="@/assets/map/deviceDetail/scd.png" alt="" class="w-102 h-213 pt-34" draggable="false" />
        </template>
        <Image v-else :src="baseInfo.installImages[0].url" class="w-102 h-213 pt-34" :preview-src-list="baseInfo.installImages.map(item => item.url)" />
      </template>
      <div v-else class="w-102 h-213 pt-34" />
      <aside v-loading="baseInfoLoading">
        <header v-if="!isMB" class="ml-5 mb-13">
          <span text="#DAF4EC">累计杀虫数量：</span>
          <span v-if="[null, undefined].includes(baseInfo.killCount)">--</span>
          <CountTo v-else :start-val="0" :end-val="baseInfo.killCount" class="value font-din text-22 text-#29FFBB" />
        </header>
        <section v-if="isMB" class="detail empty relative !w-507 !h-260 flex flex-col justify-center items-center gap-y-31 select-none">
          <img src="@/assets/common/no-img.webp" alt="" class="w-86 h-76 object-cover" />
          <span text="14 #647D6B">MB型号类暂无数据</span>
        </section>
        <section v-else class="detail relative w-507 h-260 items-center auto-rows-30" p="y-10 x-20" b="1 solid #0D7559" grid="~ cols-[173px_260px]" text="14">
          <div class="absolute top-19 right-20 flex items-center gap-x-5">
            <template v-if="baseInfo.online">
              <div class="w-6 h-6 bg-#28F5B4 rounded-full" />
              <span text="14 #28F5B4">在线</span>
            </template>
            <template v-else>
              <div class="w-6 h-6 bg-#FF4D4F rounded-full" />
              <span text="14 #FF4D4F">离线</span>
            </template>
          </div>
          <div v-for="item in baseInfoList" :key="item.id">
            <span text="#A5C8BD">{{ item.label }}</span>
            <template v-if="![null, undefined].includes(item.value)">
              <span text="#EDF8F3" :style="{ color: item.color }">{{ item.value }}{{ item.unit || '' }}</span>
              <!--              网络状态添加图标 -->
              <SvgIcon v-if="item.id === 15" class="text-20 ml-7" :name="`signal-${baseInfo.networkType}`" />
            </template>
            <span v-else>--</span>
          </div>
        </section>
      </aside>
    </header>

    <template v-if="!isMB">
      <header class="flex mt-20 justify-center">
        <button
          v-for="item in tabList" :key="item.id"
          class="menuBtn use-bg font-alimama -ml-20 w-139 h-56 pointer-events-auto tracking-3 pointer-events-auto"
          :class="{ active: currentTab.id === item.id }"
          text="14 #E2FFF0/50"
          @click="changeTab(item)"
        >
          {{ item.title }}
        </button>
      </header>

      <div class="flex items-center justify-between ml-20 mr-20 mt-20">
        <div class="flex items-center">
          <button
            v-for="(label, value) in dateTypeMap" :key="value"
            class="button use-bg w-61 h-39 leading-23 tracking-2"
            :class="{ active: dateType === value }"
            text="12 #86A29D"
            @click="changeDateType(value)"
          >
            {{ label }}
          </button>
        </div>
        <SelectDate v-model="date" class="ml-auto mr-10" :type="dateType" @change="dateChange" />
      </div>

      <div v-loading="chartDataLoading1 || chartDataLoading2" class="w-790 h-280">
        <Chart v-if="xData.length" ref="chartRef" class="w-850 h-full -ml-30 -mr-60" :option="option" :noMerge="true" />
        <div v-else class="empty" />
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.value {
  background: linear-gradient(180deg, #ffc053 0%, #fff0bc 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.detail {
  background: linear-gradient(0deg, rgba(7, 18, 11, 0.64) 0%, rgba(19, 48, 29, 0.64) 100%);
  border: 1px solid #0d7559;
}

.menuBtn {
  background-image: url(@/assets/layout/menu-bg.webp);
  transition: all 0.3s;

  &.active {
    color: #e2fff0;
    // 文本发光
    text-shadow: 0 0 10px #e2fff0;
    background-image: url(@/assets/layout/menu-bg-active.webp);
  }
}

.button {
  transition: 0.3s all;

  &.active {
    color: #ceffe6;
    background-image: url(@/assets/common/radio-bg.webp);
  }
}
</style>

<style lang="scss">
.lampDialog.lampDialog {
  --el-dialog-width: 844px;

  height: fit-content;
  padding: 25px;
  padding-bottom: 35px;
  background: url(@/assets/common/bg_844.webp) center / 100% 100% no-repeat;

  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
