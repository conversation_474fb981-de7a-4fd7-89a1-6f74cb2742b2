import L from 'leaflet'
import { MAP_TYPE_ID } from '@/constants'
import '@/plugins/BDLayer'

const bigeMapUrl = 'https://map.vankeytech.com:9100/bigemap.4woonc6b/tiles/{z}/{x}/{y}.png?access_token=pk.eyJ1IjoiY3VzXzdhOHc4YjZxIiwiYSI6ImRlanNpbWFtdXQ0cjc3MzAxMnZtYnRvcG4iLCJ0Ijo0fQ.WzVZjBXKP8pUxJUinfC_-eCJQC6f2bmjYIiiWRK_xyk'
const url2 = 'https://maps3.shipdt.com/vt?lyrs=y&hl=zh-CN&gl=CN&x={x}&y={y}&z={z}'

export const mapTypeList = [
  {
    id: 1,
    name: '卫星地图',
    crs: L.CRS.EPSG3857,
    layer: L.tileLayer(bigeMapUrl, {
      tileSize: 256,
      maxZoom: 20,
      maxNativeZoom: 18,
      minZoom: 6,
      bounds: [
        [50.36729928578421, 68.55468750000001],
        [-3.6286541049128727, 141.32812500000003],
      ],
    }),
  },
  {
    id: 2,
    name: '深色地图',
    crs: L.CRS.Baidu,
    layer: L.tileLayer('https://map.vankeytech.com:9100/bigemap.5n9skkkw/tiles/{z}/{x}/{y}.png?access_token=pk.eyJ1IjoiY3VzXzdhOHc4YjZxIiwiYSI6ImRlanNpbWFtdXQ0cjc3MzAxMnZtYnRvcG4iLCJ0Ijo0fQ.WzVZjBXKP8pUxJUinfC_-eCJQC6f2bmjYIiiWRK_xyk', {
      tileSize: 256,
      maxZoom: 18,
      tms: true,
      minZoom: 3,
    }),
  },
  {
    id: 3,
    name: '浅色地图',
    crs: L.CRS.Baidu,
    layer: L.tileLayer('https://map.vankeytech.com:9100/bigemap.1cwjdiiu/tiles/{z}/{x}/{y}.png?access_token=pk.eyJ1IjoiY3VzXzdhOHc4YjZxIiwiYSI6ImRlanNpbWFtdXQ0cjc3MzAxMnZtYnRvcG4iLCJ0Ijo0fQ.WzVZjBXKP8pUxJUinfC_-eCJQC6f2bmjYIiiWRK_xyk', {
      tileSize: 256,
      maxZoom: 18,
      tms: true,
      minZoom: 3,
    }),
  },

]

export class MapCore {
  static map: L.Map

  init(id: string): L.Map {
    const currentMapId = Number(localStorage.getItem(MAP_TYPE_ID)) || 1
    const currentMap = mapTypeList.find(item => item.id === currentMapId)
    const map = new L.Map(id, {
      center: [31.626748, 105.817774],
      zoom: 13,
      maxBounds: [[50.36729928578421, 68.55468750000001], [-3.6286541049128727, 141.32812500000003]],
      attributionControl: false,
      zoomControl: false,
      minZoom: 6,
      crs: currentMap.crs,
    })

    const layer = currentMap.layer
    layer.addTo(map)
    MapCore.map = map
    return map
  }
}
