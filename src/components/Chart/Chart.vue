<script lang="ts" setup>
import type { ECBasicOption } from 'echarts/types/dist/shared'
import type { PropType } from 'vue'
import { noop, useDebounceFn, useEventListener } from '@vueuse/core'
import * as echarts from 'echarts'
import { onMounted, onUnmounted } from 'vue'

const props = defineProps({
  option: {
    type: Object as PropType<echarts.EChartsCoreOption>,
    required: true,
  },
  isSvg: {
    type: Boolean,
    default: true,
  },
  noMerge: {
    type: Boolean,
    default: false,
  },
})

// 公共配置选项
const baseOption: ECBasicOption = {
  backgroundColor: 'transparent', // 去掉背景色
}

let offWatchEffect = () => {}

onMounted(() => {
  initChart()

  // 当option发生变化后重新执行setOption()
  offWatchEffect = watchEffect(() => {
    props.option && myChart?.setOption({ ...baseOption, ...props.option }, props.noMerge)
  })
})

// 初始化图表
const chartsRef = shallowRef<HTMLDivElement>()
let myChart: echarts.ECharts
let offResize = noop
const onResize = useDebounceFn(() => {
  myChart.resize()
}, 100)

function initChart() {
  myChart = echarts.init(chartsRef.value, 'dark', {
    renderer: props.isSvg ? 'svg' : 'canvas',
  })
  offResize = useEventListener('resize', onResize)
}

// 在容器节点被销毁时，总是应调用 echartsInstance.dispose 以销毁实例释放资源，避免内存泄漏
onUnmounted(() => {
  offWatchEffect()
  offResize()
  echarts.dispose(myChart)
})
function getChart() {
  return myChart
}

defineExpose({
  getChart,
})
</script>

<template>
  <div ref="chartsRef" class="chart" />
</template>

<style scoped lang="scss">
// :where选择器，权值为0，方便外部覆盖
:where(.chart) {
  width: 100%;
  height: 100%;
}
</style>
