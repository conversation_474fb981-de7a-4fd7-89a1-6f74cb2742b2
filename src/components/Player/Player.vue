<script setup lang="ts">
/**
 * 官方文档
 * @see https://jessibuca.com/api.html
 *
 */

import crypto from 'node:crypto'
import { ElMessage } from 'element-plus'
import { MonitorAPI } from '@/api/monitor/monitor.ts'

const { videoUrl, loading = false, deviceId, channelId, live = true } = defineProps<{
  loading?: boolean
  videoUrl?: string
  deviceId: string
  channelId: string
  // 是否是直播，回放的话不需要云台控制和对讲按钮
  live?: boolean
}>()
const emit = defineEmits<{ (e: 'timeUpdate', delta: number) }>()

let player: Jess<PERSON><PERSON> = null

let preTimestamp = 0
function timeUpdate(e: number) {
  const delta = e - preTimestamp
  if (delta < 500) {
    emit('timeUpdate', e - preTimestamp)
  }

  preTimestamp = e
}

let offWatch = () => {}

const videoContainerRef = useTemplateRef('videoContainerRef')

const showSelfButton = ref(false)
onMounted(() => {
  player = new Jessibuca({
    container: videoContainerRef.value,
    videoBuffer: 0.1, // 缓存时长
    isResize: false,
    supportDblclickFullscreen: true,
    keepScreenOn: true, // 开启屏幕常亮，在手机浏览器上, canvas标签渲染视频并不会像video标签那样保持屏幕常亮
    hotKey: true, // 否开启键盘快捷键
    controlAutoHide: true, // 底部控制台是否自动隐藏
    useWebFullScreen: true, // 是否使用web全屏(旋转90度)（只会在移动端生效）
    loadingText: '加载中...',
    decoder: '/jessibuca/decoder.js',
    useMSE: true,
    debug: false,
    showBandwidth: false, // 显示网速
    operateBtns: {
      fullscreen: true,
      screenshot: true,
      play: true,
      audio: true,
    },
    isNotMute: false,
    useWCS: true,
    autoWasm: true,
  })

  player.on('timeUpdate', timeUpdate)

  /** 创建一个位于图标集合最前列位置的元素，方便将自定义图标放在最前面，默认会放在最后面 */
  const div = document.createElement('div')
  div.classList.add('jessibuca-controls-right-first', 'flex')
  const aimEl = document.querySelector('.jessibuca-controls-right')
  aimEl.insertBefore(div, aimEl.firstChild)

  offWatch = watch(
    () => videoUrl,
    (url) => {
      if (url) {
        player.play(videoUrl)
      }
    },
    { immediate: true },
  )

  showSelfButton.value = true
})

// 对讲相关状态
const talkbackVisible = ref(false)
const broadcastStatus = ref(-1) // -2: 正在释放资源, -1: 默认状态, 0: 等待接通, 1: 接通成功
const broadcastRtc = ref(null)

const holderShow = ref(false)
function toggleTalkback() {
  talkbackVisible.value = !talkbackVisible.value
  if (talkbackVisible.value) {
    // 如果打开对讲，则关闭云台控制
    holderShow.value = false
  }
  else {
    stopBroadcast()
  }
}

// 云台控制相关状态
function togglePtzControl() {
  holderShow.value = !holderShow.value
  // 如果打开云台控制，则关闭对讲
  if (holderShow.value) {
    talkbackVisible.value = false
    stopBroadcast()
  }
}

function closePtzControl() {
  holderShow.value = false
}

/** S 对讲功能 */
function startTalkback() {
  if (broadcastStatus.value === -1) {
    // 默认状态，开始对讲
    broadcastStatus.value = 0
    // 发起语音对讲，默认使用对讲模式
    MonitorAPI.voiceBroadcast(deviceId, channelId, false).then((res) => {
      const streamInfo = res.streamInfo
      startBroadcast(streamInfo.rtcs)
    }).catch((error) => {
      console.error('启动对讲失败:', error)
      broadcastStatus.value = -1
    })
  }
  else if (broadcastStatus.value === 1) {
    // 正在对讲中，停止对讲
    stopBroadcast()
  }
}

function startBroadcast(url: string) {
  // 获取推流鉴权Key
  MonitorAPI.userInfo().then((data) => {
    if (data == null) {
      broadcastStatus.value = -1
      return
    }

    const pushKey = data.pushKey
    // 获取推流鉴权KEY
    url += `&sign=${crypto.createHash('md5').update(pushKey, 'utf8').digest('hex')}`
    console.log(`开始语音对讲：${url}`)

    // 创建WebRTC连接
    broadcastRtc.value = new ZLMRTCClient.Endpoint({
      debug: true,
      zlmsdpUrl: url,
      simulecast: false,
      useCamera: false,
      audioEnable: true,
      videoEnable: false,
      recvOnly: false,
    })

    // 监听WebRTC事件
    broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT, (e) => {
      console.error('不支持webrtc', e)
      ElMessage.error('不支持webrtc, 无法进行语音对讲')
      broadcastStatus.value = -1
    })

    broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR, (e) => {
      console.error('ICE 协商出错')
      ElMessage.error('ICE 协商出错')
      broadcastStatus.value = -1
    })

    broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED, (e) => {
      console.error('offer anwser 交换失败', e)
      ElMessage.error(`offer anwser 交换失败${e}`)
      broadcastStatus.value = -1
    })

    broadcastRtc.value.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE, (e) => {
      console.log('连接状态改变', e)
      switch (e) {
        case 'connecting': {
          broadcastStatus.value = 0
          break
        }

        case 'connected': {
          broadcastStatus.value = 1
          break
        }

        case 'disconnected': {
          broadcastStatus.value = -1
          break
        }
      // No default
      }
    })

    broadcastRtc.value.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, (e) => {
      console.log('捕获流失败', e)
      ElMessage.error(`捕获流失败${e}`)
      broadcastStatus.value = -1
    })
  }).catch((error) => {
    ElMessage.error(error)
    broadcastStatus.value = -1
  })
}

function stopBroadcast() {
  if (broadcastRtc.value) {
    broadcastRtc.value.close()
    broadcastRtc.value = null
  }

  broadcastStatus.value = -1
  // MonitorAPI.voiceBroadcastStop(deviceId, channelId)
}

function getBroadcastStatusText() {
  if (broadcastStatus.value === -2) {
    return '正在释放资源'
  }

  if (broadcastStatus.value === -1) {
    return '点击开始对讲'
  }

  if (broadcastStatus.value === 0) {
    return '等待接通中...'
  }

  if (broadcastStatus.value === 1) {
    return '请说话'
  }

  return '点击开始对讲'
}
/** E 对讲功能 */

/** S 云台控制功能 */
/**
 * 操控云台
 */
function control(direction: string) {
  MonitorAPI.ptzControl(deviceId, channelId, direction).then((res) => {
    console.log('云台控制', res)
  })
}
/** E 云台控制功能 */

defineExpose({
  closePtzControl,
})

onUnmounted(() => {
  offWatch()

  // 清理对讲资源
  if (broadcastRtc.value) {
    stopBroadcast()
  }

  if (player) {
    player.destroy().finally(() => {
      player = null
    })
  }
})
</script>

<template>
  <div class="liveVideo">
    <div ref="videoContainerRef" v-loading="loading" class="bg-black" />
    <template v-if="showSelfButton && live">
      <!--      云台控制按钮 -->
      <teleport to=".jessibuca-controls-right-first">
        <div class="jessibuca-ptz jessibuca-controls-item relative">
          <i class="jessibuca-icon jessibuca-icon-ptz op-80" hover="op-100" @click="togglePtzControl" />
          <span class="icon-title-tips"><span class="icon-title">云台控制</span></span>
          <div v-if="holderShow" class="holder-box absolute -top-20 left-1/2 -translate-y-full -translate-x-1/2">
            <h2 class="title" />
            <i class="close-icon" @click="closePtzControl" />
            <div class="content-box">
              <div class="btn-item left" @mousedown.stop="control('left')" @mouseup.stop="control('stop')" />
              <div class="btn-item left-top" @mousedown.stop="control('upleft')" @mouseup.stop="control('stop')" />
              <div class="btn-item top" @mousedown.stop="control('up')" @mouseup.stop="control('stop')" />
              <div class="btn-item top-right" @mousedown.stop="control('upright')" @mouseup.stop="control('stop')" />
              <div class="btn-item right" @mousedown.stop="control('right')" @mouseup.stop="control('stop')" />
              <div class="btn-item right-bottom" @mousedown.stop="control('downright')" @mouseup.stop="control('stop')" />
              <div class="btn-item bottom" @mousedown.stop="control('down')" @mouseup.stop="control('stop')" />
              <div class="btn-item bottom-left" @mousedown.stop="control('downleft')" @mouseup.stop="control('stop')" />
            </div>
            <div class="control-item">
              <span>缩放</span>
              <div class="btn-item left" @mousedown.stop="control('zoomin')" @mouseup.stop="control('stop')" />
              <div class="btn-item right" @mousedown.stop="control('zoomout')" @mouseup.stop="control('stop')" />
            </div>
          </div>
        </div>
      </teleport>

      <!--      对讲按钮 -->
      <teleport to=".jessibuca-controls-right-first">
        <div class="jessibuca-talkback jessibuca-controls-item relative">
          <i class="jessibuca-icon jessibuca-icon-talkback op-80" hover="op-100" @click="toggleTalkback" />
          <span class="icon-title-tips"><span class="icon-title">对讲</span></span>
          <div v-if="talkbackVisible" role="button" class="talkbackBox use-bg flex flex-col items-center gap-y-8 absolute -top-20 left-1/2 -translate-y-full -translate-x-1/2 w-120 h-120 select-none" @click.stop="startTalkback">
            <SvgIcon name="microphone" class="text-25 text-#A3C6A6 mt-40" />
            <span class="not-italic" text="!11 #A3C6A6">{{ getBroadcastStatusText() }}</span>
          </div>
        </div>
      </teleport>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.jessibuca-icon-talkback {
  background: url(@/assets/monitor/microphone.svg) center / 100% 100% no-repeat;
}

.jessibuca-icon-ptz {
  background: url(@/assets/monitor/<EMAIL>) center / 100% 100% no-repeat;
}

.talkbackBox {
  background-image: url(@/assets/monitor/yaokong.svg);
}

.holder-box {
  width: 278px;
  height: 361px;
  background: url(@/assets/monitor/<EMAIL>) no-repeat center;
  background-size: contain;

  .title {
    width: 278px;
    height: 32px;
    background: url(@/assets/monitor/<EMAIL>) no-repeat center;
    background-size: contain;
  }

  .close-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 32px;
    height: 33px;
    cursor: pointer;
    background: url(@/assets/monitor/<EMAIL>) no-repeat center;
    background-size: contain;
  }

  .content-box {
    position: absolute;
    top: 50px;
    left: 50%;
    width: 180px;
    height: 180px;
    background: url(@/assets/monitor/<EMAIL>) no-repeat center;
    background-size: contain;
    transform: translateX(-50%);

    .btn-item {
      position: absolute;
      width: 20px;
      height: 20px;
      cursor: pointer;

      &.left {
        top: 50%;
        left: 20px;
        transform: translateY(-50%);
      }

      &.left-top {
        top: 40px;
        left: 40px;
      }

      &.top {
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
      }

      &.top-right {
        top: 40px;
        right: 40px;
      }

      &.right {
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
      }

      &.right-bottom {
        right: 40px;
        bottom: 40px;
      }

      &.bottom {
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
      }

      &.bottom-left {
        bottom: 40px;
        left: 40px;
      }
    }
  }

  .control-item {
    position: absolute;
    top: 230px;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 152px;
    height: 28px;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
    background: url(@/assets/monitor/<EMAIL>) no-repeat center;
    background-size: contain;
    transform: translateX(-50%);

    .btn-item {
      width: 28px;
      height: 28px;
      cursor: pointer;
      border-radius: 5px;
    }

    .left {
      position: absolute;
      top: 0;
      left: 0;
    }

    .right {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}
</style>
