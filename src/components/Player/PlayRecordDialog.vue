<script setup lang="ts">
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor/monitor.ts'

const { deviceId, channelId, startTime, endTime } = defineProps<{ deviceId?: string, channelId: string, startTime: string, endTime: string }>()
const dialogVisible = defineModel<boolean>()

const videoUrl = ref()
const monitorLoading = ref(false)
function queryMonitor(startTime: string, endTime: string) {
  monitorLoading.value = true
  MonitorAPI.videoReplay(deviceId, channelId, startTime, endTime)
    .then((res) => {
      videoUrl.value = res.wss_flv
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

function closeDialog() {
  videoUrl.value = null
}

queryMonitor(startTime, endTime)

/** S 修改视频播放进度 */
const startTimestamp = ref(new Date(startTime).getTime())
const endTimestamp = ref(new Date(endTime).getTime())
const currentTimestamp = ref(0)
function changeTime() {
  const startTime = dayjs(currentTimestamp.value).format('YYYY-MM-DD HH:mm:ss')
  const endTime = dayjs(endTimestamp.value).format('YYYY-MM-DD HH:mm:ss')
  queryMonitor(startTime, endTime)
}

function formateTooltip(value: number) {
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

function timeUpdate(delta: number) {
  if (monitorLoading.value)
    return
  currentTimestamp.value += delta
}
/** E 修改视频播放进度 */
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close class="monitor-dialog" align-center @close="closeDialog">
    <template #header>
      <DialogTitle title="录像回放" @close="dialogVisible = false" />
    </template>
    <div v-loading="monitorLoading" class="relative w-full h-530 m-auto mt-20 px-20">
      <Player class="size-full" :live="false" :video-url="videoUrl" :channelId="channelId" :deviceId="deviceId" @timeUpdate="timeUpdate" />
    </div>
    <footer class="py-10 mx-20">
      <el-slider v-model="currentTimestamp" :min="startTimestamp" :max="endTimestamp" :format-tooltip="formateTooltip" @change="changeTime" />
    </footer>
  </el-dialog>
</template>

<style scoped lang="scss"></style>

<style lang="scss">
.monitor-dialog {
  &.el-dialog {
    --el-dialog-width: 1019px;

    height: 684px;
    padding: 25px;
    background: url(@/assets/common/bg_1180.webp) center / 100% 100% no-repeat;
  }
}
</style>
