<script setup lang="ts">
import { MonitorAPI } from '@/api/monitor/monitor.ts'

const { deviceId, channelId } = defineProps<{ deviceId?: string, channelId: string }>()
const dialogVisible = defineModel<boolean>()

const videoUrl = ref()
const monitorLoading = ref(false)
function queryMonitor() {
  monitorLoading.value = true
  MonitorAPI.play(deviceId, channelId)
    .then((res) => {
      videoUrl.value = res.wss_flv
    })
    .finally(() => {
      monitorLoading.value = false
    })
}

watch(
  dialogVisible,
  (val) => {
    if (val) {
      queryMonitor()
    }
  },
  { immediate: true },
)
</script>

<template>
  <el-dialog v-model="dialogVisible" append-to-body :show-close="false" destroy-on-close class="monitor-dialog" align-center>
    <template #header>
      <DialogTitle title="监控详情" @close="dialogVisible = false" />
    </template>
    <Player :video-url="videoUrl" :loading="monitorLoading" :device-id="deviceId" :channelId="channelId" class="h-580" />
  </el-dialog>
</template>

<style scoped lang="scss"></style>

<style lang="scss">
.monitor-dialog {
  &.el-dialog {
    --el-dialog-width: 1149px;

    background: url(@/assets/common/bg_1180.webp) center / 100% 100% no-repeat;
  }
}
</style>
