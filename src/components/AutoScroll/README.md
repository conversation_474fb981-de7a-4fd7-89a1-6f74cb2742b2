# AutoScroll 自动滚动组件

一个基于 Vue 3 和 Element Plus 的自动滚动列表组件，支持正向和反向滚动，具有鼠标悬停暂停、自适应容器高度等功能。

## 功能特性

- ✨ 支持正向和反向自动滚动
- 🎯 鼠标悬停时自动暂停滚动
- 📱 响应式设计，自适应容器尺寸变化
- ⚡ 基于 `requestAnimationFrame` 的流畅动画
- 🔄 滚动到底部后自动重置并继续滚动
- 🎛️ 可配置滚动速度和停留时间
- 📦 TypeScript 支持，泛型数据类型

## 基础用法

```vue
<script setup>
import AutoScroll from '@/components/AutoScroll/AutoScroll.vue'

const dataList = ref([
  { id: 1, name: '数据项 1', value: 10 },
  { id: 2, name: '数据项 2', value: 20 },
  { id: 3, name: '数据项 3', value: 30 },
  // ... 更多数据
])
</script>

<template>
  <div>
    <AutoScroll :list="dataList" class="!h-300">
      <div v-for="(item, index) in list" :key="item.id" class="item flex items-center h-50 px-10" text="15 #242424">
        <div class="index shrink-0 w-20 h-23 grid place-items-center mr-14 font-bold" text="16 #7B8085">{{ index + 1 }}</div>
        <div class="truncate mr-5">{{ item.name }}</div>
        <div class="ml-auto">{{ item.value }}</div>
      </div>
    </AutoScroll>
  </div>
</template>

<style> </style>
```

## API 文档

### Props

| 参数        | 类型      | 默认值  | 说明                                   |
| ----------- | --------- | ------- | -------------------------------------- |
| `list`      | `T[]`     | -       | **必需**，要滚动的数据列表             |
| `reverse`   | `boolean` | `false` | 是否反向滚动（从下往上）               |
| `immediate` | `boolean` | `false` | 是否立即开始滚动（不等待初始停留时间） |

### Slots

| 插槽名    | 说明                                    |
| --------- | --------------------------------------- |
| `default` | 自定义列表项内容，通常配合 `v-for` 使用 |

## 使用示例

### 反向滚动

```vue
<template>
  <AutoScroll :list="messages" reverse>
    <li v-for="msg in messages" :key="msg.id" class="message-item">
      {{ msg.content }}
    </li>
  </AutoScroll>
</template>
```

### 立即开始滚动

```vue
<template>
  <AutoScroll :list="data" immediate>
    <li v-for="item in data" :key="item.id">
      {{ item.text }}
    </li>
  </AutoScroll>
</template>
```
