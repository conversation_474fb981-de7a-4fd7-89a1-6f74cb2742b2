<script lang="ts" setup generic="T">
import { noop, useDebounceFn, useEventListener } from '@vueuse/core'
import { ElScrollbar } from 'element-plus'
import { ref } from 'vue'
import { useOnceFn } from '@/utils'

const {
  list,
  reverse = false,
  immediate = false,
} = defineProps<{
  list: T[]
  // 反向滚动
  reverse?: boolean
  immediate?: boolean
}>()

// 滚动时间间隔，滚动速度
const intervalTime = 60
// 交替时间间隔
const alternateTime = 3000
const innerContainerRef = shallowRef<HTMLLIElement>()
const scrollContainerRef = ref<HTMLElement>()
const containerRef = shallowRef<InstanceType<typeof ElScrollbar>>()
// 给底部设置paddingBottom以便数据能滚动到最后一条
const paddingBottom = ref(0)
const setPaddingOnce = useOnceFn(() => {
  nextTick(() => {
    const { height } = innerContainerRef.value.firstElementChild.getBoundingClientRect()
    paddingBottom.value = scrollContainerRef.value.clientHeight - height
    scrollContainerRef.value.style.paddingBottom = `${paddingBottom.value}px`
    scrollContainerRef.value.scrollTop = Number(reverse) * scrollContainerRef.value.scrollHeight
  })
})

const onResize = useDebounceFn(() => {
  if (!list.length)
    return
  const { height } = innerContainerRef.value.firstElementChild.getBoundingClientRect()
  paddingBottom.value = scrollContainerRef.value.clientHeight - height
  scrollContainerRef.value.style.paddingBottom = `${paddingBottom.value}px`
}, 100)

// 页面尺寸变化后重新设置paddingBottom
useEventListener(window, 'resize', onResize)
let timer
let requestTimer // requestAnimationFrame定时器
let waitTimer // 等待定时器
let offWatch = noop
onMounted(() => {
  offWatch = watch(
    () => list,
    () => {
      if (!list?.length)
        return
      offTimer()
      // 这里只有在nextTick中才能获取到元素正确的scrollHeight
      nextTick(() => {
        scrollContainerRef.value = containerRef.value.wrapRef
        // 判断是否有纵向滚动条
        const hasYScroll = scrollContainerRef.value.scrollHeight > scrollContainerRef.value.clientHeight
        if (!hasYScroll)
          return // 没有纵向滚动条就不用自动滚动
        setPaddingOnce()
        const timeout = immediate ? 0 : Number(!reverse) * alternateTime

        waitTimer = setTimeout(() => {
          requestAnimationFrame(runAutoScroll)
        }, timeout)
      })
    },
    { immediate: true },
  )
})

// 自动滚动
function runAutoScroll() {
  const step = reverse ? -1 : 1
  timer = setTimeout(() => {
    scrollContainerRef.value.scrollTop += step
    // 判断滚动到底部
    if (
      reverse
        ? scrollContainerRef.value.scrollTop <= 0
        : Math.ceil(scrollContainerRef.value.scrollTop + scrollContainerRef.value.clientHeight) >= scrollContainerRef.value.scrollHeight
    ) {
      offTimer()
      // 到底部后停3秒再滚动
      waitTimer = setTimeout(() => {
        scrollContainerRef.value.scrollTop = Number(reverse) * scrollContainerRef.value.scrollHeight
        requestTimer = requestAnimationFrame(runAutoScroll)
      }, alternateTime)
      return
    }

    requestTimer = requestAnimationFrame(runAutoScroll)
  }, intervalTime)
}

// 关闭自动滚动
function offTimer() {
  cancelAnimationFrame(requestTimer)
  clearTimeout(timer)
  clearTimeout(waitTimer)
  requestTimer = null
  timer = null
  waitTimer = null
}

// 鼠标移入时取消动画
function handleMouseEnter() {
  offTimer()
}

// 鼠标移出时开始动画
function handleMouseLeave() {
  if (!scrollContainerRef.value)
    return
  // 判断是否有纵向滚动条
  const hasYScroll = scrollContainerRef.value.scrollHeight > scrollContainerRef.value.clientHeight
  if (!hasYScroll)
    return // 没有纵向滚动条就不用自动滚动
  runAutoScroll()
}

onUnmounted(() => {
  offTimer()
  offWatch()
})
</script>

<template>
  <ElScrollbar ref="containerRef" view-class="h-full" class="text-12px text-#7F7F7F" tag="ul" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <template v-if="list?.length">
      <div ref="innerContainerRef">
        <slot />
      </div>
    </template>
    <li v-else class="size-full flex flex-col justify-center select-none">
      <span text="#150f00 center">暂无数据</span>
    </li>
  </ElScrollbar>
</template>

<style lang="scss" scoped>
:deep(.el-scrollbar__bar) {
  display: none;
}
</style>
