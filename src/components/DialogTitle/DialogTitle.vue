<script setup lang="ts">
const { title } = defineProps<{
  title: string
}>()
const emit = defineEmits<{
  (e: 'close'): void
}>()
</script>

<template>
  <div class="flex items-center -mx-10">
    <img src="@/assets/common/title-icon.webp" draggable="false" class="w-29 h-29 object-cover" alt="" />
    <div class="title italic tracking-2" font="bold" text="18 #CEFFE6">{{ title }}</div>
    <SvgIcon name="close" class="text-20 ml-auto cursor-pointer mt-10 text-#CEFFE6" @click="emit('close')" />
  </div>
</template>

<style scoped lang="scss">

</style>
