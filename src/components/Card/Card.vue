<script setup lang="ts">
const { title } = defineProps<{ title: string }>()
</script>

<template>
  <div>
    <header class="title font-shi use-bg w-448 -my-10 flex items-center pl-64 pr-34 tracking-2" text="18 #F4F9FF">
      <div class="text" :style="{ '--title': `'${title}'` }">{{ title }}</div>
    </header>
    <section class="content pl-26">
      <slot />
    </section>
  </div>
</template>

<style scoped lang="scss">
.title {
  z-index: -1;
  height: var(--card-title-height);
  background-image: url(@/assets/layout/title-bg.webp);

  .text {
    background: linear-gradient(74deg, rgba(255, 255, 255, 1) 0%, rgba(184, 255, 227, 1) 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;

    &::before {
      position: absolute;
      z-index: -1;
      text-shadow: 0 0 1.5px white;
      content: var(--title);
    }
  }
}

.content {
  height: calc(100% - var(--card-title-height) + 10px);
}
</style>
