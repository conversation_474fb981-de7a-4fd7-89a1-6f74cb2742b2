# SelectYearRange 年份范围选择组件

## 组件属性

| 属性名    | 类型       | 默认值 | 必填 | 说明                                                      |
| --------- | ---------- | ------ | ---- | --------------------------------------------------------- |
| `range`   | `number`   | `1`    | 否   | 年份选择范围限制，不传则不限制，传值则限制为前后 range 年 |
| `v-model` | `string[]` | -      | 是   | 双向绑定的年份数组，格式为 `['2023', '2024']`             |

## 组件事件

| 事件名   | 参数            | 说明                                   |
| -------- | --------------- | -------------------------------------- |
| `change` | `val: string[]` | 年份范围改变时触发，返回选中的年份数组 |

## 使用示例

### 基础用法

```vue
<script setup lang="ts">
const yearRange = ref(['2023', '2024'])

function handleYearChange(val: string[]) {
  console.log('选中的年份范围:', val)
}
</script>

<template>
  <SelectYearRange v-model="yearRange" @change="handleYearChange" />
</template>
```

### 限制选择范围

```vue
<script setup lang="ts">
const yearRange = ref(['2023', '2024'])

function handleYearChange(val: string[]) {
  console.log('选中的年份范围:', val)
}
</script>

<template>
  <!-- 限制只能选择前后2年的范围 -->
  <SelectYearRange
    v-model="yearRange"
    :range="2"
    @change="handleYearChange"
  />
</template>
```
