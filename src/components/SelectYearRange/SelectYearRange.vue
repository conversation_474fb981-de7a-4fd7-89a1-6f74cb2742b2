<script setup lang="ts">
const { range = 1, clearable = false } = defineProps<{
  // 范围,不传则不限制，传则限制为前后range年
  range?: number
  clearable?: boolean
}>()
const emit = defineEmits<{
  (e: 'change', val: string[]): void
}>()

const value = defineModel<string[]>()

function change(val: string[]) {
  emit('change', val)
}

const firstDate = ref<Date>()
function calendarChange(val) {
  firstDate.value = val[1] ? null : val[0]
}

function disabledDate(date: Date) {
  const isAfterDate = date > new Date()
  if (!firstDate.value || !range)
    return isAfterDate
  const currentYear = firstDate.value.getFullYear()
  const year = date.getFullYear()
  // 禁用当前年份之前的年份
  return isAfterDate || (year < currentYear - 1) || (year > currentYear + 1) || (year === currentYear)
}
</script>

<template>
  <div class="relative pointer-events-auto w-fit h-fit !cursor-pointer">
    <el-date-picker
      v-model="value"
      value-format="YYYY"
      type="yearrange"
      class="h-full !w-full opacity-0 !absolute inset-0 !cursor-pointer"
      :clearable="clearable"
      :disabled-date="disabledDate"
      @calendar-change="calendarChange"
      @change="change"
    />
    <div class="select-bg use-bg min-w-129 h-39 flex pt-12 pl-15 tracking-1 justify-center pointer-events-none" text="12 #CEFFE6">
      <span />
      <span class="truncate" text="center">{{ value.join('~') }}</span>
      <SvgIcon name="down-arrow" class="ml-auto mr-18 text-12" />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-range-input) {
  cursor: pointer;
}

.select-bg {
  background: url(@/assets/common/select-bg.webp);
}
</style>
