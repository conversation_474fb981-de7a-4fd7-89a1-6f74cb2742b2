<script setup lang="ts">
import dayjs from 'dayjs'

const emit = defineEmits(['change'])

const value = defineModel<string[]>()

function change() {
  emit('change', value.value)
}

function disabledDate(date: Date) {
  return date > new Date()
}
</script>

<template>
  <div class="relative pointer-events-auto w-fit h-fit">
    <el-date-picker
      v-model="value"
      value-format="YYYY-MM-DD HH:mm:ss"
      type="datetimerange"
      class="h-full !w-full opacity-0 !absolute inset-0 !cursor-pointer"
      :clearable="false"
      :default-time="[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]"
      :disabled-date="disabledDate"
      @change="change"
    />
    <div class="select-bg use-bg min-w-190 h-45 flex pt-13 pl-20 tracking-1 justify-center pointer-events-none" text="14 #CEFFE6">
      <span />
      <span class="truncate mr-20" text="center">{{ dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss') }} 至 {{ dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss') }}</span>
      <SvgIcon name="date" class="text-14 ml-auto mr-24" />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input__inner, ) {
  cursor: pointer;
}

:deep(.el-input__wrapper) {
  cursor: pointer;
}

.select-bg {
  background: url(@/assets/common/select-bg-352.webp);
}
</style>
