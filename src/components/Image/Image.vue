<script setup lang="ts">
import type { ImageProps } from 'element-plus'
import { getFileUrl } from '@/api/system/file'

const props = defineProps<Partial<ImageProps>>()

const addressableSrc = ref('')
watch(
  () => props.src,
  () => {
    if (!props.src)
      return
    getFileUrl(props.src).then((url) => {
      addressableSrc.value = url
    })
  },
  { immediate: true },
)

const addressablePreviewSrcList = ref([])
watch(
  () => props.previewSrcList,
  () => {
    if (!props.previewSrcList?.length)
      return
    addressablePreviewSrcList.value = []
    for (const [index, url] of props.previewSrcList.filter(Boolean).entries()) {
      getFileUrl(url).then((res) => {
        addressablePreviewSrcList.value[index] = res
      })
    }
  },
  { immediate: true },
)
</script>

<template>
  <el-image
    v-if="addressableSrc"
    fit="cover"
    v-bind="$attrs"
    :src="addressableSrc"
    preview-teleported
    hide-on-click-modal
    :preview-src-list="addressablePreviewSrcList"
  />
</template>

<style scoped lang="scss"></style>
