<script setup lang="ts">
import { MapCore, mapTypeList } from '@/components/Map/mapCore.ts'
import { MAP_TYPE_ID } from '@/constants'

const { left = true, right = true } = defineProps<{ left?: boolean, right?: boolean }>()

const currentMap = ref(Number(localStorage.getItem(MAP_TYPE_ID)) || 1)
let preLayer = mapTypeList.find(item => item.id === currentMap.value).layer
function changeMap() {
  // 移除之前图层
  const map = MapCore.map
  map.removeLayer(preLayer)
  const config = mapTypeList.find(item => item.id === currentMap.value)
  config.layer.addTo(map)
  preLayer = config.layer
  localStorage.setItem(MAP_TYPE_ID, `${config.id}`)
  location.reload()
}
</script>

<template>
  <div class="transition-area">
    <!--    左侧 -->
    <aside v-if="left" class="left relative pointer-events-auto flex flex-col">
      <!--      <transition -->
      <!--        enter-active-class="animate__animated animated animate__flipInY" -->
      <!--        leave-active-class="animate__animated animated animate__flipOutY" -->
      <!--        mode="out-in" -->
      <!--      > -->
      <transition
        enter-active-class="animate__animated animated animate__fadeInLeft"
        leave-active-class="animate__animated animated animate__fadeOutLeft"
        mode="out-in"
      >
        <!--      <transition name="el-zoom-in-center" mode="out-in"> -->
        <slot name="left" />
      </transition>

      <!--      左侧操作按钮 -->
      <div class="absolute -right-30 translate-x-full top-10 pointer-events-auto flex">
        <slot name="left-control" />
        <Select v-model="currentMap" :options="mapTypeList" :filterable="false" @change="changeMap" />
      </div>
    </aside>
    <!--    中间 -->
    <section class="center">
      <header class="header">
        <slot name="center-header" />
      </header>
      <footer class="pointer-events-auto footer">
        <transition
          enter-active-class="animate__animated animated animate__fadeInUp"
          leave-active-class="animate__animated animated animate__fadeOutDown"
          mode="out-in"
        >
          <slot name="center-footer" />
        </transition>
      </footer>
    </section>
    <!--    右侧 -->
    <aside v-if="right" class="right relative pointer-events-auto flex flex-col mr-35">
      <!--      右侧操作按钮 -->
      <!--      <div class="absolute -left-10 -translate-x-full top-10 pointer-events-auto"> -->
      <!--        &lt;!&ndash;        <slot name="right-control" /> &ndash;&gt; -->
      <!--        <Select v-model="currentMap" :options="mapTypeList" :filterable="false" @change="changeMap" /> -->
      <!--      </div> -->
      <transition
        enter-active-class="animate__animated animated animate__fadeInRight"
        leave-active-class="animate__animated animated animate__fadeOutRight"
        mode="out-in"
      >
        <!--      <transition name="el-zoom-in-center" mode="out-in"> -->
        <slot name="right" />
      </transition>
    </aside>
  </div>
</template>

<style scoped lang="scss">
.animated {
  animation-duration: 0.3s; //动画持续时间
}
</style>
