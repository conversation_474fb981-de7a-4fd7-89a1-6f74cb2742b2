<script setup lang="ts">
import type { DateType } from '@/constants'
import dayjs from 'dayjs'

const { type = 'year' } = defineProps<{ type?: DateType }>()
const emit = defineEmits(['change'])

const value = defineModel<any>()

function change(val: any) {
  emit('change', val)
}

const valueFormatMap: Record<DateType, string> = {
  year: 'YYYY',
  month: 'YYYY-MM',
  // week: 'YYYY-MM',
  day: 'YYYY-MM-DD',
}

function disabledDate(date: Date) {
  return date > new Date()
}

watch(
  () => type,
  () => {
    value.value = dayjs(value.value).format(valueFormatMap[type])
    change(value.value)
  },
)
</script>

<template>
  <div class="relative pointer-events-auto w-fit h-fit">
    <el-date-picker
      v-model="value"
      :value-format="valueFormatMap[type]"
      :type="type === 'day' ? 'date' : type"
      class="h-full !w-full opacity-0 !absolute inset-0 !cursor-pointer"
      :clearable="false"
      :disabledDate="disabledDate"
      @change="change"
    />
    <div class="select-bg use-bg min-w-190 h-45 flex pt-13 pl-20 tracking-1 justify-center pointer-events-none" text="14 #CEFFE6">
      <span />
      <span v-if="value" class="truncate" text="center">{{ value }}</span>
      <span v-else class="w-40" />
      <SvgIcon name="date" class="text-14 ml-auto mr-24" />
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-input__inner, ) {
  cursor: pointer;
}

:deep(.el-input__wrapper) {
  cursor: pointer;
}

.select-bg {
  background: url(@/assets/common/select-bg-190.webp);
}
</style>
