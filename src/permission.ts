import router from '@/router'
import { useUserStore } from '@/store/user'
import { redirectToLogin } from '@/utils'
import { getToken, removeToken } from '@/utils/auth.ts'
import NProgress from 'nprogress'

// 白名单路由
const whiteList = new Set([])

router.beforeEach(async (to, from, next) => {
  /**
   * 利用最新的过渡API添加路由切换时的过渡效果 startViewTransition
   *@see https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition
   */
  // Fallback for browsers that don't support View Transitions:
  if (document.startViewTransition) {
    // With View Transitions:
    document.startViewTransition(() => {
      return true
    })
  }

  NProgress.start()
  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已登录，跳转到首页
      next({ path: '/' })
    }
    else {
      const userStore = useUserStore()
      const hasRoles = userStore.user.roles && userStore.user.roles.length > 0

      if (hasRoles) {
        next()
      }
      else {
        // const permissionStore = usePermissionStore()
        try {
          const res = await userStore.getUserInfo()
          // 是否有大数据权限
          if (!res.menus.includes('300000')) {
            // 移除 token 并重定向到登录页，携带当前页面路由作为跳转参数
            removeToken()
            redirectToLogin(to)
            return
          }

          // const dynamicRoutes = await permissionStore.generateRoutes()
          // dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route))
          next({ ...to, replace: true })
        }
        catch {
          // 移除 token 并重定向到登录页，携带当前页面路由作为跳转参数
          removeToken()
          redirectToLogin(to)
        }
      }
    }
  }
  else {
    // 未登录
    if (whiteList.has(to.path)) {
      next() // 在白名单，直接进入
    }
    else {
      // 不在白名单，重定向到登录页
      redirectToLogin(to)
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
