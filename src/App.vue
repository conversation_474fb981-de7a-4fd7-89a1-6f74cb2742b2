<script setup lang="ts">
import { useDebounceFn, useEventListener } from '@vueuse/core'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { getRatio, useAppStore } from '@/store/app.ts'

/**
 * 监听页面尺寸变化，获取当前页的缩放比例
 */
const appStore = useAppStore()
const debouncedResize = useDebounceFn(() => {
  appStore.ratio = getRatio()
}, 100)

useEventListener('resize', () => {
  debouncedResize()
})
</script>

<template>
  <ElConfigProvider :locale="zhCn">
    <RouterView />
  </ElConfigProvider>
</template>

<style scoped lang="scss">

</style>
