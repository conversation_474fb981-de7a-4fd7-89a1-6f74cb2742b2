@use 'reset';
@use 'bigData';
@use 'fonts';
@use 'common';
@use 'element';
@use 'variables';
@use 'transition';

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  color: #d0ebdd;
  text-align: justify;
  color-scheme: light dark;
  background-color: #242424;
  line-break: anywhere;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

body {
  min-width: 100vw;
  min-height: 100vh;
}

#app {
  background: url(@/assets/layout/bg.webp) center / 100% 100% no-repeat;
}
