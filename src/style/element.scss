body {
  --el-color-primary: #1b9097;
  --el-color-primary-light-3: #37a1a3;
  --el-color-primary-light-5: #58b0ae;
  --el-color-primary-light-7: #7dbdb9;
  --el-color-primary-light-8: #a5c9c6;
  --el-color-primary-light-9: #c9d6d5;
  --el-color-primary-dark-2: #0f6870;
  --el-border-color: #579174;
  --el-border-color-hover: #b9f7e4;
  --el-input-focus-border-color: #b9f7e4;
  --el-input-height: 38px;
  --el-input-bg-color: transparent;
  --el-bg-color-overlay: rgb(0, 72, 49);
  // 弹窗背景色
  --el-mask-color: transparent;
  --el-border-color-light: #15ac7b;
  --el-component-size: 40px;
  --el-text-color-regular: #9dd9c2;

  //--el-text-color-primary: #9dd9c2;

  .el-table__expand-icon {
    color: #b9f7e4;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #033b29;
  }

  .el-select-dropdown__item.selected {
    color: #033b29;
  }

  .el-select-dropdown__item {
    color: #cde8fd;
  }

  .el-time-spinner__item.is-active:not(.is-disabled) {
    color: #cde8fd;
  }

  .el-select-dropdown__item.is-hovering {
    background-color: #033b29;
  }

  .el-picker-panel__icon-btn {
    color: #9dd9c2;
  }

  .el-picker-panel {
    color: #9dd9c2;
  }

  .el-time-panel__btn {
    color: #9dd9c2;
  }

  .el-date-range-picker,
  .el-date-picker {
    --el-datepicker-active-color: #0eb36b;
    --el-datepicker-inrange-bg-color: #005e3a;
    --el-datepicker-text-color: #86c1aa;
    --el-datepicker-inrange-hover-bg-color: #0eb36b;
  }

  :is(.el-year-table, .el-month-table) td.disabled .el-date-table-cell__text {
    color: #86c1aa;
    background-color: #0e6443;
  }

  .el-slider__runway {
    background-color: gray;
  }

  .el-date-table td.disabled .el-date-table-cell {
    color: #86c1aa;
    background-color: #0e6443;
  }

  .el-input.is-disabled .el-input__wrapper {
    background-color: transparent;
  }

  // 分页器样式修改
  .el-pagination {
    --el-pagination-button-color: #a7d2bb;
    --el-pagination-button-bg-color: transparent;
    --el-pagination-hover-color: white;
    --el-pagination-border-radius: 4px;
    --el-disabled-bg-color: transparent;
    --el-text-color-placeholder: #a7d2bb;
    --el-pagination-button-disabled-bg-color: transparent;
    --el-pagination-bg-color: transparent;

    .el-pagination__total {
      color: #a7d2bb;
    }

    .el-pager li {
      border: 1px solid #55795e;

      &.is-active {
        color: #a7d2bb;
        background-color: rgba(0, 125, 32, 0.4);
      }
    }

    .btn-prev,
    .btn-next {
      border: 1px solid #55795e;
    }

    .el-select__input {
      height: 36px;
    }

    .el-select__wrapper {
      width: 100px;
      outline: none;
      box-shadow: none;
    }

    .el-select {
      outline: none;
    }

    .el-select__placeholder {
      color: #a7d2bb;
    }

    .el-pagination__sizes {
      //outline: none;
    }
  }

  .el-select__wrapper {
    height: 100%;
    background-color: transparent;
  }

  .el-input {
    --el-input-bg-color: transparent;
  }

  // 弹窗
  .el-dialog {
    --el-dialog-width: 1426px;

    height: 78vh;
    padding: 35px;
    background: url(@/assets/common/dialog-bg.webp) center / 100% 100% no-repeat;

    // 输入框
    .el-input,
    .el-select {
      // 输入框样式修改
      --el-input-bg-color: transparent;
      --el-input-focus-border-color: #b9f7e4;
      --el-input-text-color: #b9f7e4;
      --el-input-width: 289px;

      background: linear-gradient(90deg, rgba(19, 80, 47, 0) 0%, #13502f 100%);

      .el-input__inner {
        height: 38px;
      }
    }

    .title {
      // 文字发光
      text-shadow:
        -1px -1px 3px rgba(1, 231, 129, 0.5),
        -1px 1px 3px rgba(136, 136, 136, 0.5),
        1px -1px 3px rgba(136, 136, 136, 0.5),
        1px 1px 3px rgba(1, 231, 129, 0.5);
    }
  }

  // 时间范围选择器
  .el-date-editor {
    --el-date-editor-daterange-width: 400px;
    --el-input-focus-border-color: #b9f7e4;

    background: linear-gradient(90deg, rgba(19, 80, 47, 0) 0%, #13502f 100%);

    .el-range-input {
      color: #b9f7e4;
    }
  }

  // el-table
  .el-table {
    --el-table-header-bg-color: transparent;
    --el-table-bg-color: transparent;
    --el-table-row-hover-bg-color: transparent;
    --el-table-tr-bg-color: transparent;
    --el-table-text-color: #b6dfd2;
    --el-table-border: none;

    thead {
      color: #45e79d;
    }

    .el-table__header {
      tr {
        height: 54px;
        background: linear-gradient(90deg, rgba(19, 80, 47, 0) 0%, #13502f 100%);
      }
    }

    .el-table__inner-wrapper::before {
      height: 0;
    }

    .el-checkbox {
      --el-checkbox-bg-color: rgba(87, 145, 116, 0.45);
      --el-checkbox-input-border: #579174;
      --el-checkbox-checked-bg-color: #006a2a;
      --el-checkbox-checked-input-border-color: #579174;

      border: 1px solid #579174;
      border-radius: 4px;

      .el-checkbox__inner::after {
        left: 5px;
      }
    }
  }

  .el-table--small {
    .el-table__header {
      tr {
        height: 24px;
        background: #13502f;
      }
    }
  }

  .el-table--striped .el-table__body tr.el-table__row--striped {
    background: linear-gradient(90deg, rgba(19, 80, 47, 0) 0%, #13502f 100%);

    td.el-table__cell {
      background: transparent;
    }
  }

  .detail-table {
    .el-table__header {
      tr {
        height: 46px;
        background: transparent;
      }
    }
  }

  .el-tooltip {
    .el-tag {
      color: #d9fff3;
      background-color: #004831;
    }
  }
}

// 表格无数据展示
.el-table {
  .cell {
    &:empty {
      &::before {
        content: '--';
      }
    }
  }
}
