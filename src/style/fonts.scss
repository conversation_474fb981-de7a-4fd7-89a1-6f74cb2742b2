@font-face {
  font-family: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ianTi;
  font-style: normal;
  font-weight: normal;
  src: url(@/assets/fonts/ShiShangZhongHeiJianTi.TTF);
}

.font-shi {
  font-family: Shi<PERSON><PERSON><PERSON><PERSON>HeiJianTi, serif;
}

@font-face {
  font-family: DIN;
  src: url(@/assets/fonts/DIN.OTF);
}

.font-din {
  font-family: DIN, serif;
}

@font-face {
  font-family: DOUYU;
  src: url(@/assets/fonts/douyuFont.otf);
}

.font-douyu {
  font-family: DOUYU, serif;
}

@font-face {
  font-family: TRENDS;
  src: url(@/assets/fonts/TRENDS.TTF);
}

.font-trends {
  font-family: TRENDS, serif;
}

// 优设标题黑
@font-face {
  font-family: YousheTitleHei;
  src: url(@/assets/fonts/YouSheBiaoTiHei.ttf);
}

.font-youShe {
  padding: 0.1em 0;
  font-family: YousheTitleHei, serif;
}

// 庞门正道标题体
@font-face {
  font-family: PangMenZhengDao;
  src: url(@/assets/fonts/PangMenZhengDao.ttf);
}

.font-pangMen {
  font-family: PangMenZhengDao, serif;
}

// AlimamaShuHeiTi
@font-face {
  font-family: AlimamaShuHeiTi;
  src: url(@/assets/fonts/ALIMAMASHUHEITI-BOLD.OTF);
}

.font-alimama {
  font-family: AlimamaShuHeiTi, serif;
}
