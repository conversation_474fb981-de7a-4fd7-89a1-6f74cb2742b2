<script setup lang="ts">
import type { RouteRecordRawExt } from '@/router'
import { OverviewAPI } from '@/api/overview/overview.ts'
import Map from '@/components/Map/Map.vue'
import { useAppStore } from '@/store/app.ts'
import { useUserStore } from '@/store/user.ts'
import { emitter } from '@/utils/events.ts'
import Header from './components/Header.vue'

const route = useRoute()
const currentSecondLevelRouteList = computed(() => route.matched[0].children.filter(item => !(item as RouteRecordRawExt).hidden))

const currentSecondLevelRoute = computed(() => route.matched[1])

const userStore = useUserStore()
const appStore = useAppStore()
const hasDeviceTypes = computed(() => appStore.hasDeviceTypes)
// 如果是超级管理员用户，默认所有设备类型都存在
if (userStore.user.userType === 0) {
  appStore.hasDeviceTypes = {
    1: true,
    2: true,
    3: true,
    4: true,
  }
}
else {
  OverviewAPI.statistics().then((res) => {
    for (const item of res) {
      if (item.total) {
        appStore.hasDeviceTypes[item.type] = true
      }
    }
  })
}

const router = useRouter()
function to(path: string) {
  emitter.emit('farmlandClick', null)
  emitter.emit('cancelFarmlandSelect')
  router.push(path)
}

function toFarmLink() {
  window.open('https://www.baidu.com/')
}
</script>

<template>
  <div class="appContainer use-bg">
    <Map v-if="!route.meta.noMap" />
    <section class="app-container size-screen relative z-1 flex flex-col" :class="{ 'pointer-events-none': !route.meta.noMap }">
      <Header class="shrink-0" />
      <div v-if="!route.meta.noMap" class="flex mx-25">
        <template v-for="item in currentSecondLevelRouteList" :key="item.path">
          <button
            v-if="!item.meta.deviceType || hasDeviceTypes[item.meta.deviceType]"
            class="menuBtn use-bg font-alimama min-w-139 px-50 h-56 pointer-events-auto tracking-3 pointer-events-auto -ml-20"
            first="ml-0"
            :class="{ active: currentSecondLevelRoute.path === item.path }"
            text="16 #E2FFF0/50"
            @click="to(item.path)"
          >
            {{ item.meta.title }}
          </button>
        </template>
        <button
          class="menuBtn use-bg font-alimama min-w-139 px-50 h-56 pointer-events-auto tracking-3 pointer-events-auto -ml-20"
          text="16 #E2FFF0/50"
          @click="toFarmLink"
        >
          智慧农业产品
        </button>
      </div>
      <div class="main-container use-bg grow overflow-hidden">
        <router-view v-slot="{ Component }" class="w-full h-full">
          <keep-alive v-if="!route.meta.noMap" :include="[]">
            <component :is="Component" class="" />
          </keep-alive>
          <transition
            v-else
            enter-active-class="animate__animated animated animate__slideInLeft"
            leave-active-class="none"
            mode="out-in"
            appear
          >
            <keep-alive :include="[]">
              <component :is="Component" class="" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
.appContainer {
  background-image: url(@/assets/layout/bg.webp);
}

.menuBtn {
  background-image: url(@/assets/layout/menu-bg.webp);
  transition: all 0.3s;

  &.active {
    color: #e2fff0;
    // 文本发光
    text-shadow: 0 0 10px #e2fff0;
    background-image: url(@/assets/layout/menu-bg-active.webp);
  }
}
</style>
