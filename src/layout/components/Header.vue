<script setup lang="ts">
import type { ChartPoint } from '@/api/common/type'
import dayjs from 'dayjs'
import { CommonAPI } from '@/api/common/common.ts'
import { AuthAPI } from '@/api/system/auth.ts'
import { DOMAIN } from '@/constants'
import { useRafInterval } from '@/hooks/useRafInterval.ts'
import { appRoutes } from '@/router'
import { useUserStore } from '@/store/user.ts'

const userStore = useUserStore()
const title = computed(() => `${userStore.user.customer?.aliasName || '物华'}智慧农业大数据平台`)

/**
 * 显示当前时间
 */
const time = ref(Date.now())
useRafInterval(() => {
  time.value = Date.now()
}, 500)
const route = useRoute()
const router = useRouter()
function to(path: string) {
  router.push(path)
}

const currentFistLevelRoute = computed(() => route.matched[0])

function logout() {
  AuthAPI.logout().then(() => {
    userStore.logout()
  })
}

function toWeb() {
  window.location.href = DOMAIN
}

/**
 * 获取天气
 */
const weather = ref<ChartPoint>({})
const location = ref('')
CommonAPI.weather({ historyDays: 0, featureDays: 1 }).then((res) => {
  location.value = res?.location
  weather.value = res?.points?.[0] || {}
})
</script>

<template>
  <div class="appHeader use-bg flex h-106 pointer-events-auto">
    <div class="appTitleContainer use-bg h-115 w-fit pr-120 min-w-30vw max-w-50vw">
      <div class="appTitle font-youShe mt-12 ml-48 cursor-pointer select-none truncate" text="40" :style="{ '--title': `'${title}'` }" @click="to('/')">{{ title }}</div>
    </div>
    <section class="-ml-20 flex">
      <div
        v-for="item in appRoutes" :key="item.path"
        role="button"
        class="menuBtn use-bg font-youShe w-227 h-85 pl-82 leading-81 cursor-pointer select-none -ml-82"
        :class="{ active: currentFistLevelRoute.path === item.path }"
        first="ml-0"
        text="22 #CBDBDB/80"
        @click="to(item.children[0].path)"
      >
        {{ item.meta.title }}
      </div>
    </section>

    <button class="btn use-bg ml-auto w-130 h-49 mt-17 pb-7" text="white" @click="toWeb">管理后台</button>
    <aside class="mt-31 flex items-center h-fit ml-22" text="14 #CDFFEE">
      <span>{{ dayjs(time).format('YYYY-MM-DD HH:mm:ss') }}</span>
      <span class="op-34 mx-15">|</span>

      <div v-if="location" class="flex items-center">
        <i text="18" class="mr-5" :class="`qi-${weather.icon}`" />
        <span>{{ weather.tempMin }}℃</span>
        <span class="mx-3" text="18">~</span>
        <span>{{ weather.tempMax }}℃</span>
        <span class="ml-5 text-12">{{ location }}</span>
      </div>

      <SvgIcon name="turn-off" class="mr-23 ml-19 text-20 text-#7ED5B3 cursor-pointer" @click="logout" />
    </aside>
  </div>
</template>

<style scoped lang="scss">
.appHeader {
  position: relative;

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: -1;
    display: block;
    height: 85px;
    content: '';
    background: url(@/assets/layout/header-bg.png) center / 100% 100% no-repeat;
  }
}

.appTitleContainer {
  position: relative;
  z-index: 1;
  background: url(@/assets/layout/header-title-bg.png);
}

.appTitle {
  background: linear-gradient(180deg, rgb(109, 255, 181, 1) 0%, white 80%);
  background-clip: text;
  -webkit-text-fill-color: transparent;

  &::before {
    position: absolute;
    z-index: -1;
    text-shadow: 0 6px 5px rgb(0, 13, 10, 0.52);
    content: var(--title);
  }
}

.menuBtn {
  transition: all 0.3s;
}

.active {
  color: #fff;
  background-image: url(@/assets/layout/active-bg.webp);
}

.btn {
  background-image: url(@/assets/layout/btn-bg.webp);
}
</style>
