import type { FormRules } from 'element-plus'

export function getState<T = any>() {
  return reactive({
    tableList: [] as T[],
    total: 0,
    loading: false,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    } as any as PageQuery<T>,
    formData: {} as Partial<T>,
    dialogVisible: false,
    detailDialog: false,
    title: '',
    fullscreenLoading: false,
    rules: {} as FormRules<T>,
  })
}
