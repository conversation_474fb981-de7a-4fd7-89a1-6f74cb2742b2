import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import router from '@/router'
import { useUserStoreHook } from '@/store/user'
import { getToken, removeToken } from '@/utils/auth.ts'
import { redirectToLogin } from '@/utils/index.ts'
import axios from 'axios'
import { ElMessage, ElNotification } from 'element-plus'

/**
 * 响应码枚举
 */
export enum ResultEnum {
  /**
   * 成功
   */
  SUCCESS = 200,
  /**
   * 错误
   */
  ERROR = 'B0001',

  /**
   * 令牌无效或过期
   */
  TOKEN_INVALID = 401,
}

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 50000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' },
})

// 保存所有的AbortController，用于取消请求
const abortControllerMap: Record<string, AbortController> = {}
// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = getToken()
    if (accessToken) {
      config.headers.Authorization = accessToken
    }

    /** 默认接口带有去重功能，若不需要去重，将headers中withoutUnique属性值设为true即可 */
    if (!config.headers.withoutUnique) {
      // 取消重复请求
      const key = config.url
      if (abortControllerMap[key]) {
        abortControllerMap[key].abort()
        abortControllerMap[key] = undefined
      }

      const controller = new AbortController()
      abortControllerMap[key] = controller
      config.signal = controller.signal
    }

    return config
  },
  (error: any) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
let flag = false
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查配置的响应类型是否为二进制类型（'blob' 或 'arraybuffer'）, 如果是，直接返回响应对象
    if (response.config.responseType === 'blob' || response.config.responseType === 'arraybuffer') {
      return response
    }

    const { code, data, msg } = response.data
    if (code === ResultEnum.SUCCESS) {
      return data
    }

    ElMessage.error(msg || '系统出错')
    return Promise.reject(new Error(msg || 'Error'))
  },
  (error: any) => {
    if (error.code === 'ERR_CANCELED')
      return
    // 异常处理
    if (error.response.data) {
      const { code, msg } = error.response.data
      if (code === ResultEnum.TOKEN_INVALID) {
        if (flag) {
          return
        }

        flag = true
        if (import.meta.env.DEV) {
          ElMessage.error({
            message: '您的会话已过期，请前往客户端复制token到代码中，详情请阅读README.md',
            duration: 10000,
          })
        }
        else {
          ElMessage.error({
            message: '您的会话已过期，请重新登录',
          })
        }

        removeToken()
        redirectToLogin(router.currentRoute.value)
      }
      else {
        ElMessage.error(msg || '系统出错')
      }
    }

    return Promise.reject(error.message)
  },
)

// 导出 axios 实例
export default service
