/**
 * 大数据没有单独登录系统，直接使用管理的token，所以这里的key务必保证与管理端一致
 */
const TokenKey = 'wuhuaClentAccessToken'

export function getToken(): string {
  // 是否是开发者模式
  const isDev = import.meta.env.DEV
  if (isDev) {
    // 这里的token从客户端复制，详情请查看README.md
    return 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJyb290Iiwibmlja25hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJjdXN0b21lcklkIjo0LCJjbGllbnQiOiJDVVNUT01FUiIsInVzZXJUeXBlIjoiQURNSU5JU1RSQVRPUiIsImF1dGhUeXBlIjoiUEFTU1dPUkQiLCJleHAiOjE4MTg3MzQxMDksInVzZXJJZCI6MSwiaWF0IjoxNzU4MjU0MTA5LCJhdXRob3JpdGllcyI6WyJST0xFX0FETUlOSVNUUkFUT1I6RjkwRkU3NkEtMjExMi00RDlDLUExNTUtQzIyNEVCNzVEMTQzIiwiUk9MRV9DVVNUT01FUl9ST09UIl0sImp0aSI6ImNmNGUxOWIwOWUwYjQwNjQ5YjVhNjdjMThiZjY0Y2UxIn0.vY8UHDxzWbgmFxZf2BZssyClflHpEJCBIXnkWyhr8-4'
  }

  return localStorage.getItem(TokenKey)
}

export function setToken(token: string) {
  localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  localStorage.removeItem(TokenKey)
}
