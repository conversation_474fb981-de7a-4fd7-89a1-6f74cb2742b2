import type { Emitter } from 'mitt'
import type { Planting } from '@/api/farmland/type'
import mitt from 'mitt'

type Events = {
  /**
   * 地图点击地块
   *
   */
  farmlandClick: number
  /**
   * 取消地块选中
   */
  cancelFarmlandSelect: null
  /**
   * 根据地块id地图定位并选中地块marker
   */
  selectFarmlandById: number
  /**
   * 地块详情-点击种植项
   */
  cropClick: Planting

}

export const emitter: Emitter<Events> = mitt<Events>()
