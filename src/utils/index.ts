import type { RouteLocationNormalized } from 'vue-router'
import { DOMAIN } from '@/constants'

/** 重定向到登录页 */
export function redirectToLogin(to: RouteLocationNormalized) {
  const params = new URLSearchParams(to.query as Record<string, string>)
  const queryString = params.toString()
  const redirect = queryString ? `${to.path}?${queryString}` : to.path
  window.location.replace(`${DOMAIN}/#/login?redirect=${encodeURIComponent(redirect)}&bigData=true`)
}

/**
 * 下载文件，支持传入文件流或者url
 */
export async function download(content: string | BlobPart, fileName: string) {
  try {
    let blob: Blob
    if (typeof content === 'string') {
      // 获取文件
      const response = await fetch(content)
      blob = await response.blob()
    }
    else {
      blob = new Blob([content])
    }

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'

    // 设置下载属性
    link.href = downloadUrl
    link.download = fileName

    // 触发下载
    document.body.append(link)
    link.click()

    // 清理
    link.remove()
    URL.revokeObjectURL(downloadUrl)
  }
  catch (error) {
    console.error('下载文件失败:', error)
  }
}

/**
 * 传入分钟数，输出天时分
 * @param minutes 分钟数
 * @returns 天时分
 */
export function formateMinutes(minutes: number) {
  if (!minutes)
    return '--'
  // 不足一小时，直接返回分钟
  if (minutes < 60) {
    return `${minutes}分`
  }

  // 不足一天，返回小时和分钟
  if (minutes < 1440) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分` : `${hours}小时`
  }

  // 超过一天，返回天、小时和分钟
  const days = Math.floor(minutes / 1440)
  const hours = Math.floor((minutes % 1440) / 60)
  const remainingMinutes = minutes % 60

  let result = `${days}天`

  if (hours > 0) {
    result += `${hours}小时`
  }

  if (remainingMinutes > 0) {
    result += `${remainingMinutes}分`
  }

  return result
}

/**
 * 返回一个只会调用原始函数一次的函数
 * @param {(...args: any[]) => any} fn - 要调用一次的函数
 * @returns {void} 一个只会被调用一次的函数
 */
export function useOnceFn<T extends (...args: any[]) => any>(fn: T): T {
  let flag = true
  function onceFn(this: any, ...args: Parameters<T>) {
    if (flag) {
      flag = false
      fn.apply(this, args)
    }
  }

  return onceFn as T
}
