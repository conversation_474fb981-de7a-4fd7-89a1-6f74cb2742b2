export interface RequestBase {
  /**
   * 设备ID
   */
  deviceId?: number
  /**
   * 地块ID
   */
  farmlandId?: number
  /**
   * years  array[integer] 查询的年份
   */
  years?: Array
  /**
   * insectName  病虫名字
   */
  insectName?: string
  year?: string
}

/**
 * ISAChartVO
 */
export interface InsectChart {
  /**
   * 折线图
   */
  lineCharts?: LineChart[]
  /**
   * 饼图信息
   */
  pie?: PieVO[]
}

export interface LineChart {
  label?: string
  line: Key[]
}

/**
 * key
 */
export interface Key {
  x?: string
  y?: number
}

/**
 * PieVO
 */
export interface PieVO {
  name?: string
  value?: number
}

/**
 * 虫情一体机运行日志实体
 *
 * ISAAnalyzeLogVO
 */
export interface AnalyzeLog {
  startTime?: string
  endTime?: string
  /**
   * 分析图片
   */
  analyzeImage?: ISAImg
  /**
   * 分析结果
   */
  analyzeResult?: InsectSituationAnalyzeResultBO[]
  /**
   * 公司名称
   */
  companyName?: string
  /**
   * 客户名称
   */
  customerName?: string
  /**
   * 设备ID
   */
  deviceId?: number
  /**
   * 设备名称
   */
  deviceName?: string
  /**
   * 流水号
   */
  imgSerialNum?: string
  /**
   * 病虫名称
   */
  insectName?: string
  /**
   * 病虫总数
   */
  insectTotal?: string
  /**
   * 原图
   */
  rawImage?: ISAImg
  /**
   * 设备编号
   */
  serialNum?: string
  /**
   * 更新时间
   */
  time?: string
}

/**
 * 分析图片
 *
 * ISAImg
 *
 * 原图
 */
export interface ISAImg {
  /**
   * 原图
   */
  raw?: string
  /**
   * 图片大小
   */
  size?: number
  /**
   * 缩略图
   */
  thumb?: string
}

export interface InsectSituationAnalyzeResultBO {
  count?: number
  name?: string
}
