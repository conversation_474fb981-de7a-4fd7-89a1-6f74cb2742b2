import type { ChartData } from '#/index'
import type { Analyze<PERSON><PERSON>, InsectChart, RequestBase } from './type'
import type { LineChartData } from '@/api/lamp/type'
import type { DateType } from '@/constants'
import request from '@/utils/request'

export const InsectAPI = {
  // 病虫数量汇总
  insectCount(unit: DateType, params: RequestBase) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/isa/summary/insect-count/${unit}`,
      method: 'get',
      params,
    })
  },
  // 病虫种类汇总
  insectTypeCount(params: RequestBase) {
    return request({
      url: `/customer/bigdata/device/isa/summary/insect-type`,
      method: 'get',
      params,
    })
  },

  // 病虫对比
  insectContrast(params: RequestBase) {
    return request({
      url: `/customer/bigdata/device/isa/contrast/insect`,
      method: 'get',
      params,
    })
  },
  // 病虫数量分布
  insectDist(params: RequestBase) {
    return request({
      url: `/customer/bigdata/device/isa/dis/insect`,
      method: 'get',
      params,
    })
  },

  //  // 病虫环比
  //  insectMom(params: RequestBase) {
  //   return request({
  //     url: `/customer/bigdata/device/isa/mom/insect`,
  //     method: 'get',
  //     params,
  //   })
  // },

  // 图表
  chart(deviceId: number, xUnit: string, startDate: string, endDate: string) {
    return request<any, InsectChart>({
      url: `/customer/bigdata/device/isa/${deviceId}/chart/${xUnit}`,
      method: 'get',
      params: { startDate, endDate },
    })
  },

  // 分析记录
  analyzeLog(params: PageQuery<AnalyzeLog>) {
    return request<any, Records<AnalyzeLog>>({
      url: '/customer/bigdata/device/isa/running-log/page',
      method: 'get',
      params,
    })
  },

}
