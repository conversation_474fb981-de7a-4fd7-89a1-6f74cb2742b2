import type {
  Devi<PERSON>,
  <PERSON><PERSON>O<PERSON>,
  <PERSON>ceRequest,
  DeviceRequest2,
  Farmland,
  LampDetail,
  WeatherData,
  WeatherRequest,
} from './type'
import type { DeviceType } from '@/constants'
import request from '@/utils/request'

export const CommonAPI = {

  // 设备编号下拉选项
  deviceOption(params: DeviceRequest) {
    return request<any, DeviceOption[]>({
      url: '/device/common/options',
      method: 'get',
      params,
    })
  },

  // 设备列表
  deviceList(params?: DeviceRequest2) {
    return request<any, Device[]>({
      url: '/customer/bigdata/home/<USER>',
      method: 'get',
      params,
    })
  },

  // 地块列表
  farmlandList() {
    return request<any, Farmland[]>({
      url: '/customer/bigdata/farmland/listByCustomer',
      method: 'get',
    })
  },

  // 获取设备信息
  deviceDetail<T = LampDetail>(deviceId: number, deviceType: DeviceType) {
    return request<any, T>({
      url: `/customer/bigdata/device/${deviceId}/detail`,
      method: 'get',
      params: { deviceType },
    })
  },

  // 获取天气
  weather(params: WeatherRequest) {
    return request<any, WeatherData>({
      url: '/customer/bigdata/device/smd/weather',
      method: 'get',
      params,
      headers: { withoutUnique: true },
    })
  },

}
