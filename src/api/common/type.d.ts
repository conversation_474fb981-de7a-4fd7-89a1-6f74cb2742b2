import type { DeviceType } from '@/constants'

export interface DeviceRequest2 {
  /**
   * 区域id
   */
  areaId?: number
  /**
   * 设备类型
   */
  deviceType?: DeviceType
  /**
   * 地块ID
   */
  farmlandId?: number
  /**
   * 在线状态 true 在线 false 离线
   */
  online?: boolean
}
export interface Device {
  /**
   * code
   */
  code?: string
  deviceCount?: number
  deviceType?: number
  deviceTypeName?: string
  /**
   * id
   */
  id?: number
  isAlarm?: boolean
  /**
   * 监测数据
   */
  monitorData?: Array<Array<{ [key: string]: any }[]>>
  /**
   * 名称
   */
  name?: string
  /**
   * 位置信息
   */
  point?: GeoPoint
  /**
   * type  1 省 2 市 3 区县  4 设备
   */
  pointType?: number
  /**
   * 1 正常 0 离线 2 正常开灯，正常关灯
   */
  status?: number
  misc?: Misc
}
export interface Misc { deviceId?: string, channelId?: string, channelName?: string }

export interface DeviceRequest {
  deviceType: DeviceType
  serialNum?: string
}

export interface DeviceOption {
  customerName?: string
  deviceId?: number
  serialNum?: string
  type?: string
}

/**
 * 地块 VO
 */
export interface Farmland {
  /**
   * 地块中心点位
   */
  address?: string
  /**
   * 区域中心点位
   */
  coordinate?: GeoPoint
  /**
   * 当期作物
   */
  cropNames?: string
  /**
   * 客户 ID
   */
  customerId?: number
  /**
   * 描述
   */
  description?: string
  id?: number
  /**
   * 地块面积(亩)
   */
  mu?: number
  /**
   * 地块名称
   */
  name?: string
  /**
   * 区域范围
   */
  range?: GeoPolygon
  respPerson?: string
  /**
   * 负责人联系电话
   */
  respPhone?: string
}

/**
 * 区域范围
 *
 * GeoPolygon
 */
export interface GeoPolygon {
  coordinates?: GeoPoint[]
}

export interface LampDetail {
  installImages?: FileInfo[]
  /**
   * 电池电量
   */
  batteryLevel?: number
  /**
   * 充电电量
   */
  chargeAh?: string
  /**
   * 充电功率
   */
  chargeW?: string
  /**
   * 设备控制
   */
  controlConfig?: ControlConfig
  /**
   * 实时位置
   */
  coordinate?: GeoPoint
  /**
   * 电流 (ma)，负数正在充电
   */
  current?: string
  /**
   * 允许开启(OUT_1)
   */
  disabled?: boolean
  /**
   * 是否倾倒
   */
  dump?: boolean
  /**
   * 风扇电流
   */
  fanCurrent?: number
  /**
   * 强制开启（OUT_2）
   */
  forced?: boolean
  /**
   * 湿度
   */
  humidity?: string
  /**
   * 杀虫次数
   */
  killCount?: number
  /**
   * 是否开灯
   */
  light?: boolean
  /**
   * 光控 白天：1        夜晚 ： 0  正常：2
   */
  lightControl?: number
  /**
   * 灯电流
   */
  lightCurrent?: number
  /**
   * 位置更新开关
   */
  locateEnabled?: boolean
  /**
   * 定位模式
   * 0 无效定位
   * 1 卫星定位
   * 2 基站定位
   */
  locateMode?: number
  /**
   * 网络模式
   * 1 2G
   * 2 2.5G
   * 3 3G
   * 4 4G
   * 5 5G
   */
  networkType?: number
  /**
   * 是否在线
   */
  online?: boolean
  /**
   * 雨控 雨天：1        晴天：0  正常：2
   */
  rainControl?: number
  /**
   * 运行时长(分钟)
   */
  runningTime?: number
  /**
   * 信号强度（0-31）
   */
  signalQuality?: number
  /**
   * 太阳能电池板电流
   */
  solarPanelsCurrent?: number
  /**
   * 是否开启静态模式
   */
  staticMode?: boolean
  /**
   * 温度
   */
  temperature?: string
  /**
   * 数据更新时间
   */
  time?: string
  /**
   * 电压 (V)
   */
  voltage?: string
}

/**
 * 设备控制
 */
export interface ControlConfig {
  /**
   * 正常模式
   */
  normalMode?: NormalMode
  /**
   * 休眠模式
   */
  sleepMode?: SleepMode
  workHours?: [string, string][]
}

/**
 * 正常模式
 */
export interface NormalMode {
  /**
   * 截止时间
   */
  endTime?: string
  /**
   * 拍照间隔时间
   */
  interval?: number
  /**
   * 起始时间
   */
  startTime?: string
}

/**
 * 休眠模式
 */
export interface SleepMode {
  /**
   * 开关
   */
  enabled?: boolean
  /**
   * 截至日期
   */
  endDate?: string
  /**
   * 拍照时间
   */
  photoTime?: string
  /**
   * 起始日期
   */
  startDate?: string
}

export interface WeatherRequest {
  featureDays?: number
  historyDays?: number

}

/**
 * WeatherChartData
 */
export interface WeatherData {
  /**
   * 位置
   */
  location?: string
  /**
   * 图表的每个点
   */
  points?: ChartPoint[]
}

/**
 * com.vankeytech.wuhua.model.dto.WeatherChartData.ChartPoint
 *
 * ChartPoint
 */
export interface ChartPoint {
  /**
   * 日期
   */
  date?: string
  /**
   * 当天每小时相对湿度（百分比数值）
   */
  humidity?: number
  icon?: string
  /**
   * 当天月相名称
   */
  moonPhase?: string
  /**
   * 当天月升时间（格式：HH:mm），可能为空
   */
  moonrise?: string
  /**
   * 当天月落时间（格式：HH:mm），可能为空
   */
  moonset?: string
  /**
   * 降雨量 mm
   */
  precip?: number
  /**
   * 大气压强（单位：百帕）
   */
  pressure?: number
  /**
   * 当天日出时间（格式：HH:mm），在高纬度地区可能为空
   */
  sunrise?: string
  /**
   * 日落时间（格式：HH:mm），在高纬度地区可能为空
   */
  sunset?: string
  /**
   * 最高温度
   */
  tempMax?: number
  /**
   * 最低温度
   */
  tempMin?: number
  /**
   * 是否是今天
   */
  today?: boolean
}

export interface SoilDetail {
  /**
   * 大气湿度
   */
  airRh?: number
  /**
   * 大气温度
   */
  airTemp?: number
  /**
   * 电池电量
   */
  batteryLevel?: number
  /**
   * 电池电压
   */
  batteryVoltage?: number
  /**
   * 二氧化碳浓度
   */
  co2?: number
  /**
   * 电导率
   */
  conductivity?: number
  deviceId?: number
  /**
   * 蒸发量
   */
  evaporation?: number
  /**
   * 照度
   */
  illuminance?: number
  /**
   * 机器码
   */
  imei?: string
  /**
   * 钾离子
   */
  kalium?: number
  /**
   * 湿度
   */
  moisture?: string
  /**
   * 土壤湿度1
   */
  moisture1?: number
  /**
   * 土壤湿度2
   */
  moisture2?: number
  /**
   * 土壤湿度3
   */
  moisture3?: number
  /**
   * 土壤湿度4
   */
  moisture4?: number
  /**
   * 土壤湿度5
   */
  moisture5?: number
  /**
   * 氮离子
   */
  nitrogen?: number
  /**
   * 氮磷钾粒子
   */
  npk?: number
  /**
   * 在线
   */
  online?: boolean
  /**
   * PH值
   */
  ph?: number
  /**
   * 磷粒子
   */
  phosphorus?: number
  /**
   * 光合
   */
  photosynthesis?: number
  /**
   * PM10颗粒物浓度
   */
  pm10?: number
  /**
   * PM2.5颗粒物浓度
   */
  pm25?: number
  /**
   * 数字气压
   */
  pressure?: number
  /**
   * 累计辐射量
   */
  radiationAccumulated?: number
  /**
   * 雨量
   */
  rainfall?: number
  /**
   * 雨量累计
   */
  rainfallAccumulated?: number
  /**
   * 运行时间(分钟)
   */
  runningTime?: number
  /**
   * 盐分
   */
  salt?: number
  /**
   * 设备序列号
   */
  serialNum?: string
  /**
   * 辐射
   */
  solarRadiation?: number
  /**
   * 静态模式
   */
  staticMode?: boolean
  /**
   * 日照时数
   */
  sunshineHours?: number
  /**
   * 土壤温度
   */
  temperature?: string
  /**
   * 土壤温度1
   */
  temperature1?: number
  /**
   * 土壤温度2
   */
  temperature2?: number
  /**
   * 土壤温度3
   */
  temperature3?: number
  /**
   * 土壤温度4
   */
  temperature4?: number
  /**
   * 土壤温度5
   */
  temperature5?: number
  /**
   * 更新时间
   */
  time?: string
  /**
   * 紫外线指数
   */
  uvIndex?: number
  /**
   * 液位高度
   */
  waterLevel?: number
  /**
   * 风向
   */
  windDirection?: number
  /**
   * 风速
   */
  windSpeed?: number
}

export interface InsetDetail {
  /**
   * 电池电量
   */
  batteryLevel?: number
  /**
   * 设备控制
   */
  controlConfig?: ControlConfig
  /**
   * 拍照时间
   */
  lastSnapTime?: string
  /**
   * 在线
   */
  online?: boolean
  /**
   * 累计拍照次数
   */
  snapCount?: number
  /**
   * 更新时间
   */
  time?: string
  /**
   * 可用的空间 byte
   */
  totalSpace?: number
  /**
   * 运行时间（分钟）
   */
  uptime?: number
  /**
   * 已用空间 byte
   */
  usedSpace?: number
}
