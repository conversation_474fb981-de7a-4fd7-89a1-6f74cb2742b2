export interface PlantingRequest {
  /**
   * 作物名称
   */
  corpName?: string
  /**
   * 地块ID
   */
  farmlandId: number
  /**
   * 地块名称
   */
  farmlandName?: string
  /**
   * 采收结束日期 yyyy-MM-dd
   */
  harvestEndDate?: string
  /**
   * 采收开始日期 yyyy-MM-dd
   */
  harvestStartDate?: string
  /**
   * 关键字
   */
  keywords?: string
  /**
   * 种植结束 yyyy-MM-dd
   */
  plantingEndDate?: string
  /**
   * 种植开始 yyyy-MM-dd
   */
  plantingStartDate?: string
  /**
   * 采收状态:false->未完成(默认), true->已完成
   */
  status: boolean
}
export interface Planting {
  /**
   * 创建人id
   */
  createBy?: number
  operationTypeId?: number
  findDate?: string
  /**
   * 创建人名称
   */
  createByName?: string
  /**
   * 创建日期
   */
  createTime?: string
  /**
   * 客户id
   */
  customerId?: number
  /**
   * 删除标志
   */
  deleted?: boolean
  /**
   * 采收状态:0->未完成(默认), 1->已完成
   */
  doneState?: boolean
  /**
   * 预计采收日期
   */
  expectHarvestDate?: string
  /**
   * 地块id
   */
  farmlandId?: number
  /**
   * 地块名称
   */
  farmlandName?: string
  /**
   * 生长周期
   */
  growthCycle?: number
  /**
   * 生长周期名称
   */
  growthCycleName?: string
  growthCycleNumber?: number
  /**
   * 采收日期
   */
  harvestDate?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 作物图片
   */
  imageList?: FileInfo[]
  /**
   * 修改人id
   */
  modifyBy?: number
  /**
   * 修改人名称
   */
  modifyByName?: string
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 种植作物名称
   */
  name?: string
  /**
   * 种植日期
   */
  plantingDate?: string
  /**
   * 种植编号
   */
  plantingNo?: string
  /**
   * 种植人员id
   */
  plantingUser?: number
  /**
   * 种植人员名称
   */
  plantingUserName?: string
  /**
   * 备注
   */
  remark?: string
}

export interface PlantingLogRequest {
  /**
   * 查询时间
   */
  findDate?: string
  /**
   * 操作人
   */
  handlerUser?: string
  /**
   * 关键字
   */
  keywords?: string
  /**
   * 操作类型
   */
  operationTypeId?: number
  plantingId?: number
}

export interface PlantingLog {
  /**
   * 日志内容
   */
  content?: string
  createBy?: number
  createByName?: string
  /**
   * 创建时间
   */
  createTime?: string
  deleted?: boolean
  /**
   * 生长周期
   */
  growthCycle?: number
  /**
   * 生长周期 名称
   */
  growthCycleName?: string
  /**
   * 主键
   */
  id?: number
  /**
   * 图片
   */
  imageList?: FileInfo[]
  /**
   * 日志时间
   */
  logTime?: string
  modifyBy?: number
  modifyByName?: string
  modifyTime?: string
  /**
   * 操作类型主键
   */
  operationTypeId?: number
  /**
   * 操作名称
   */
  operationTypeName?: string
  /**
   * 操作人员id
   */
  personId?: number
  /**
   * 操作人名称
   */
  personName?: string
  /**
   * 种植主键
   */
  plantingId?: number
  /**
   * 种植id集合
   */
  plantingIds?: number[]
  /**
   * 是否更新到作物图片上
   */
  syncPlanting?: boolean
}

export interface OperationType {
  /**
   * 创建人
   */
  createBy?: string
  /**
   * 创建时间
   */
  createTime?: string
  /**
   * 客户表id
   */
  customerId?: number
  /**
   * 逻辑删除:0(false)->未删;1(true)->已删
   */
  deleted?: boolean
  /**
   * 状态: 0(false)-->禁用;1(true)-->启用
   */
  enable?: boolean
  /**
   * 主键
   */
  id?: number
  /**
   * 类别名称
   */
  name?: string
}

export interface DeviceParam {
  /**
   * 前端参数图标
   */
  icon?: string
  monitorTime?: string
  /**
   * 监测值code
   */
  paramCode?: string
  /**
   * 监测值名称
   */
  paramName?: string
  /**
   * 单位
   */
  unit?: string
  value?: number
}

export interface TendRequest {
  /**
   * 结束日期 横坐标播种至采收时间
   */
  endDate: string
  /**
   * 地块id
   */
  farmlandId: number
  /**
   * 参数id
   */
  paramCode: string
  /**
   * 开始日期 横坐标播种至采收时间
   */
  startDate: string
}
