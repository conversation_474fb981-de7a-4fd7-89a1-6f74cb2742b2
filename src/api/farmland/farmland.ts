import type { ChartData } from '#/index'
import type {
  DeviceParam,
  OperationType,
  Planting,
  PlantingLog,
  PlantingLogRequest,
  PlantingRequest,
  TendRequest,
} from './type'
import request from '@/utils/request'

export const FarmlandAPI = {
  // 种植情况
  planting(params: PlantingRequest) {
    return request<any, Planting[]>({
      url: '/customer/bigdata/farmland/planting',
      method: 'get',
      params,
    })
  },

  // 种植日志
  plantingLog(params: PlantingLogRequest) {
    return request<any, PlantingLog[]>({
      url: '/customer/bigdata/farmland/plantingLog',
      method: 'get',
      params,
    })
  },

  // 获取操作类别列表
  operationTypeList() {
    return request<any, OperationType[]>({
      url: '/customer/web/planting-operationType/list',
      method: 'get',
    })
  },

  // 气象墒情
  deviceParam(farmlandId: number) {
    return request<any, DeviceParam[]>({
      url: '/customer/bigdata/farmland/soil',
      method: 'get',
      params: { farmlandId },
    })
  },

  // 病虫监测
  insectCount(farmlandId: number) {
    return request<any, DeviceParam[]>({
      url: '/customer/bigdata/farmland/insectCount',
      method: 'get',
      params: { farmlandId },
    })
  },

  // 墒情趋势
  soilTrend(params: TendRequest) {
    return request<any, ChartData[]>({
      url: '/customer/bigdata/farmland/soilTrend',
      method: 'get',
      params,
    })
  },

  // 病虫趋势
  insectCountTrend(params: TendRequest) {
    return request<any, ChartData[]>({
      url: '/customer/bigdata/farmland/insectCountTrend',
      method: 'get',
      params,
    })
  },

}
