/**
 * StreamContent
 *
 * 转码后的视频流
 */
export interface Play {
  /**
   * 应用名
   */
  app?: string
  /**
   * 文件下载地址（录像下载使用）
   */
  downLoadFilePath?: DownloadFileInfo
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * HTTP-FLV流地址
   */
  flv?: string
  /**
   * HTTP-FMP4流地址
   */
  fmp4?: string
  /**
   * HLS流地址
   */
  hls?: string
  /**
   * HTTPS-FLV流地址
   */
  https_flv?: string
  /**
   * HTTPS-FMP4流地址
   */
  https_fmp4?: string
  /**
   * HTTPS-HLS流地址
   */
  https_hls?: string
  /**
   * HTTPS-TS流地址
   */
  https_ts?: string
  /**
   * IP
   */
  ip?: string
  /**
   * 流编码信息
   */
  mediaInfo?: MediaInfo
  /**
   * 流媒体ID
   */
  mediaServerId?: string
  progress?: number
  /**
   * RTC流地址
   */
  rtc?: string
  /**
   * RTCS流地址
   */
  rtcs?: string
  /**
   * RTMP流地址
   */
  rtmp?: string
  /**
   * RTMPS流地址
   */
  rtmps?: string
  /**
   * RTSP流地址
   */
  rtsp?: string
  /**
   * RTSPS流地址
   */
  rtsps?: string
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 流ID
   */
  stream?: string
  /**
   * 转码后的视频流
   */
  transcodeStream?: StreamContent
  /**
   * HTTP-TS流地址
   */
  ts?: string
  /**
   * Websocket-FLV流地址
   */
  ws_flv?: string
  /**
   * Websocket-FMP4流地址
   */
  ws_fmp4?: string
  /**
   * Websocket-HLS流地址
   */
  ws_hls?: string
  /**
   * Websocket-TS流地址
   */
  ws_ts?: string
  /**
   * Websockets-FLV流地址
   */
  wss_flv?: string
  /**
   * Websockets-FMP4流地址
   */
  wss_fmp4?: string
  /**
   * Websockets-HLS流地址
   */
  wss_hls?: string
  /**
   * Websockets-TS流地址
   */
  wss_ts?: string
}

/**
 * 文件下载地址（录像下载使用）
 *
 * DownloadFileInfo
 */
export interface DownloadFileInfo {
  httpDomainPath?: string
  httpPath?: string
  httpsDomainPath?: string
  httpsPath?: string
}

/**
 * 流编码信息
 *
 * MediaInfo
 */
export interface MediaInfo {
  /**
   * 存活时间，单位秒
   */
  aliveSecond?: number
  /**
   * 应用名
   */
  app?: string
  /**
   * 音频通道数
   */
  audioChannels?: number
  /**
   * 音频编码类型
   */
  audioCodec?: string
  /**
   * 音频采样率
   */
  audioSampleRate?: number
  /**
   * 数据产生速度，单位byte/s
   */
  bytesSpeed?: number
  /**
   * 鉴权参数
   */
  callId?: string
  /**
   * 音频采样率
   */
  duration?: number
  /**
   * FPS
   */
  fps?: number
  /**
   * 视频高度
   */
  height?: number
  /**
   * 丢包率
   */
  loss?: number
  /**
   * 在线
   */
  online?: boolean
  /**
   * unknown =
   * 0,rtmp_push=1,rtsp_push=2,rtp_push=3,pull=4,ffmpeg_pull=5,mp4_vod=6,device_chn=7,rtc_push=8
   */
  originType?: number
  /**
   * originType的文本描述
   */
  originTypeStr?: string
  /**
   * 产生流的源流地址
   */
  originUrl?: string
  /**
   * 额外参数
   */
  paramMap?: MapString
  /**
   * 观看人数
   */
  readerCount?: number
  /**
   * 协议
   */
  schema?: string
  /**
   * 服务ID
   */
  serverId?: string
  /**
   * 流ID
   */
  stream?: string
  /**
   * 视频编码类型
   */
  videoCodec?: string
  /**
   * 视频宽度
   */
  width?: number
}

/**
 * 额外参数
 *
 * MapString
 */
export interface MapString {
  key?: string
}

export interface StreamInfoRequest {
  /**
   * 应用名
   */
  app: string
  /**
   * 流媒体ID
   */
  mediaServerId: string
  /**
   * 流ID
   */
  stream: string
}

/**
 * RecordInfo
 */
export interface DeviceRecord {
  /**
   * 通道编号
   */
  channelId?: string
  count?: number
  /**
   * 设备编号
   */
  deviceId?: string
  lastTime?: Instant
  /**
   * 设备名称
   */
  name?: string
  recordList?: RecordItem[]
  /**
   * 命令序列号
   */
  sn?: string
  /**
   * 列表总数
   */
  sumNum?: number
}

/**
 * Instant
 */
export interface Instant {
  /**
   * The number of nanoseconds, later along the time-line, from the seconds field.
   * This is always positive, and never exceeds 999,999,999.
   */
  nanos?: number
  /**
   * The number of seconds from the epoch of 1970-01-01T00:00:00Z.
   */
  seconds?: number
}

/**
 * 设备录像详情
 *
 * RecordItem
 */
export interface RecordItem {
  /**
   * 录像地址(可选)
   */
  address?: string
  /**
   * 设备编号
   */
  deviceId?: string
  /**
   * 录像结束时间(可选)
   */
  endTime?: string
  /**
   * 文件路径名 (可选)
   */
  filePath?: string
  /**
   * 录像文件大小,单位:Byte(可选)
   */
  fileSize?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 录像触发者ID(可选)
   */
  recorderId?: string
  /**
   * 保密属性(必选)缺省为0;0:不涉密,1:涉密
   */
  secrecy?: number
  /**
   * 录像开始时间(可选)
   */
  startTime?: string
  /**
   * 录像产生类型(可选)time或alarm 或 manua
   */
  type?: string
}

/**
 * WVPLoginUser
 */
export interface WVPLoginUser {
  accessToken?: string
  createTime?: string
  enabled?: boolean
  /**
   * 用户
   */
  id?: number
  /**
   * 登录时间
   */
  loginTime?: string
  pushKey?: string
  role?: WVPRole
  updateTime?: string
  username?: string
}

/**
 * WVPRole
 */
export interface WVPRole {
  authority?: string
  createTime?: string
  id?: number
  name?: string
  updateTime?: string
}

/**
 * CameraVO
 */
export interface Camera {
  /**
   * 区域名称
   */
  areaName?: string
  /**
   * 批次号
   */
  batchNum?: string
  /**
   * 通道号
   */
  channelId?: string
  /**
   * 通道名称
   */
  channelName?: string
  /**
   * 客户id
   */
  customerId?: number
  /**
   * 客户名称
   */
  customerName?: string
  /**
   * 设备号
   */
  deviceId?: string
  /**
   * 对外型号名称
   */
  externalModel?: string
  id?: number
  /**
   * 设备型号id
   */
  innerModel?: number
  /**
   * 设备型号名称
   */
  innerModelName?: string
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 是否在线
   */
  online?: boolean
  /**
   * 设备编码(系统内部编号)
   */
  serialNum?: string
  /**
   * 快照URL
   */
  snapUrl?: string
  /**
   * 设备类型
   * 1 杀虫灯
   * 2 墒情
   * 3 虫情分析
   * 4 监控
   */
  type?: number
  /**
   * 设备类型名称
   * 映射json value
   */
  typeName?: string
}

export interface Snap {
  /**
   * 删除标志
   */
  deleted?: boolean
  /**
   * 摄像头通道国标id
   */
  deviceGbChannelId?: string
  /**
   * 摄像头设备国标id
   */
  deviceGbId?: string
  /**
   * 设备id
   */
  deviceId?: number
  /**
   * 设备名称
   */
  deviceName?: string
  id?: number
  /**
   * 截图信息
   */
  snapImage?: FileInfo
  /**
   * 截图时间
   */
  snapTime?: string
}

export interface DeviceCameraSnapRecord {
  startDate?: string
  endDate?: string
  /**
   * 删除标志
   */
  deleted?: boolean
  /**
   * 摄像头通道国标id
   */
  deviceGbChannelId?: string
  /**
   * 摄像头设备国标id
   */
  deviceGbId?: string
  /**
   * 设备id
   */
  deviceId?: number
  /**
   * 设备名称
   */
  deviceName?: string
  id?: number
  /**
   * 截图信息
   */
  snapImage?: FileInfo
  /**
   * 截图时间
   */
  snapTime?: string
}
