import type { ChartData } from '#/index'

export interface DateRequest {
  /**
   * 查询参数 根据不同日期不同格式 day: 2025-02-28 month: 2025-02 year: 2025
   */
  query: string
}

export interface LineChartData {
  /**
   * 图表名称
   */
  chartName?: string
  /**
   * 图表的值
   */
  values?: ChartData[]
  /**
   * x轴 单位
   */
  xunit?: string
  /**
   * Y轴 单位
   */
  yunit?: string
}

export interface Summary {
  /**
   * 湿度
   */
  humidity?: number
  /**
   * 杀虫数量
   */
  killCount?: number
  /**
   * 温度
   */
  temperature?: number
}
