import type { RequestBase } from '#/index'
import type { DateRequest, LineChartData, Summary } from './type'
import type { DateType, DateType2 } from '@/constants'
import request from '@/utils/request'

export const LampAPI = {
  // 年度数据汇总
  pestAnnual(params: RequestBase) {
    return request<any, Summary>({
      url: `/customer/bigdata/device/ikl/summary/annual`,
      method: 'get',
      params,
    })
  },
  // 杀虫数量汇总
  pestCount(unit: DateType2, params: RequestBase) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/summary/kill-count/${unit}`,
      method: 'get',
      params,
    })
  },
  // 开灯时长汇总（左侧亮灯统计）
  lightCountLeft(unit: DateType, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/summary/lightingTime/${unit}`,
      method: 'get',
      params,
    })
  },
  // 参数对比（杀虫数量、温度、湿度）
  pestContrast(param: string, params: RequestBase) {
    return request({
      url: `/customer/bigdata/device/ikl/contrast/${param}`,
      method: 'get',
      params,
    })
  },
  // 杀虫数量月环比
  pestMonthContrast(params: RequestBase) {
    return request({
      url: `/customer/bigdata/device/ikl/mom/kill-count`,
      method: 'get',
      params,
    })
  },
  // 放电电流
  current(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/discharge-current/${unit}`,
      method: 'get',
      params,
    })
  },

  // 充电电流
  chargeCurrent(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/charging-current/${unit}`,
      method: 'get',
      params,
    })
  },

  // 电压
  voltage(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/voltage/${unit}`,
      method: 'get',
      params,
    })
  },

  // 亮灯统计
  lightCount(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/light/${unit}`,
      method: 'get',
      params,
    })
  },

  // 杀虫数量
  killCount(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/kill-count/${unit}`,
      method: 'get',
      params,
    })
  },

  // 温度
  temperature(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/temperature/${unit}`,
      method: 'get',
      params,
    })
  },

  // 湿度
  humidity(unit: DateType, deviceId: number, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/ikl/${deviceId}/chart/humidity/${unit}`,
      method: 'get',
      params,
    })
  },

}
