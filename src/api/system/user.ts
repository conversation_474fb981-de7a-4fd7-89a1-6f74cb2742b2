import type { Customer } from '@/api/knowledgeStore/knowledgeStore.ts'
import request from '@/utils/request'

export const UserAPI = {
  /**
   * 获取当前登录用户信息
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  getInfo() {
    return request<any, UserInfo>({
      url: '/customer/bigdata/users/me',
      method: 'get',
    })
  },

}

/** 登录用户信息 */
export interface UserInfo {
  userType?: number
  menus?: string[]
  /** 用户ID */
  userId?: number

  /** 用户名 */
  username?: string

  /** 昵称 */
  nickname?: string

  /** 头像URL */
  avatar?: string

  /** 角色 */
  roles: string[]

  /** 权限 */
  perms: string[]

  customer?: Customer
}
