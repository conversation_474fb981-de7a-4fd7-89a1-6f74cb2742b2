import request from '@/utils/request'

export const KnowledgeAPI = {
  // 查询
  list() {
    return request<any, any>({
      url: `/customer/bigdata/knowledge-base/page`,
      method: 'get',
    })
  },

  // 知识库内容分页查询
  contentPage(params: ContentPageRequest) {
    return request<any, any>({
      url: `/customer/bigdata/knowledge-base/article/page`,
      method: 'get',
      params,
    })
  },

  // 专家库分页查询
  specialistPage(params: ContentPageRequest) {
    return request<any, any>({
      url: `/customer/bigdata/specialist/page`,
      method: 'get',
      params,
    })
  },

  // 提问并获取答案
  getAnswerWithoutStream(question: string, knowledgeBaseId: string) {
    return request<any, any>({
      url: `/customer/bigdata/ai/getAnswerWithoutStream`,
      method: 'get',
      params: { question, knowledgeBaseId },
    })
  },
}

export interface Customer {
  aliasName?: string
  address?: string
  areaId?: number
  areaName?: string
  cityCode?: number
  /**
   * 公司名称
   */
  company?: string
  /**
   * 经纬度
   */
  coordinate?: GeoPoint
  countyCode?: number
  createBy?: number
  createTime?: string
  customerUserId?: number
  deleted?: boolean
  email?: string
  id?: number
  modifyBy?: number
  modifyTime?: string
  name?: string
  password?: string
  phone?: string
  provinceCode?: number
  qq?: string
  respPerson?: string
  username?: string
  validDateEnd?: string
  validDateStart?: string
  wechat?: string
}

/**
 * 新增专家
 */
export interface SpecialistRequest {
  id?: number
  /**
   * 专家描述
   */
  content: string
  /**
   * 头像
   */
  coverImg?: string
  /**
   * 知识库id
   */
  knowledgeBaseId: number
  /**
   * 专家手机号
   */
  phone: string
  /**
   * 专家名称
   */
  title: string
  [property: string]: any
}

/**
 * 新增数据
 */
export interface ContentAddRequest {
  id?: number
  /**
   * 内容
   */
  content?: string
  /**
   * 主图
   */
  coverImg?: string
  /**
   * 知识库 ID
   */
  knowledgeBaseId?: number
  /**
   * 标签
   */
  tags?: string[]
  /**
   * 文章标题
   */
  title?: string
  [property: string]: any
}

export interface ContentPageRequest {
  /**
   * 关键字
   */
  keywords?: string
  /**
   * 知识库 ID
   */
  knowledgeBaseId: number
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  tags?: string[]
  title?: string
  [property: string]: any
}

export interface PageRequest {
  /**
   * 关键字
   */
  keywords?: string
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  [property: string]: any
}

/**
 * 新增数据
 */
export interface AddRequest {
  id?: number
  /**
   * 分发的客户id
   */
  customerIds?: number[]
  /**
   * 知识库名称
   */
  name?: string
  [property: string]: any
}
