import type { RequestBase } from '#/index'
import type { Moisture, SMDParamConfig } from './type'
import type { DateRequest, LineChartData } from '@/api/lamp/type'
import type { DateType } from '@/constants'
import request from '@/utils/request'

export const SoilAPI = {
  // 温度对比
  temperature(params: RequestBase) {
    return request<any, Moisture>({
      url: `/customer/bigdata/device/smd/contrast/temperature`,
      method: 'get',
      params,
    })
  },
  // 湿度对比
  moisture(params: RequestBase) {
    return request<any, Moisture>({
      url: `/customer/bigdata/device/smd/contrast/moisture`,
      method: 'get',
      params,
    })
  },

  // 参数年平均统计分析
  paramAvg(params: RequestBase) {
    return request<any, any>({
      url: `/customer/bigdata/device/smd/stats/param-avg`,
      method: 'get',
      params,
    })
  },
  // 参数信息列表
  parameterList(deviceId: number) {
    return request<any, SMDParamConfig[]>({
      url: `/customer/bigdata/device/smd/${deviceId}/param`,
      method: 'get',
    })
  },

  // 图表
  chart(unit: DateType, deviceId: number, paramCode: string, params: DateRequest) {
    return request<any, LineChartData>({
      url: `/customer/bigdata/device/smd/${deviceId}/chart/${paramCode}/${unit}`,
      method: 'get',
      params,
    })
  },

}
