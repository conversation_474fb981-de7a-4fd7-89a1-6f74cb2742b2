import type { ChartData } from '#/index'

export interface Moisture {
  key?: ChartData[]
}


/**
 * WeatherChartData
 */
export interface Weather {
  /**
   * 位置
   */
  location?: string
  /**
   * 图表的每个点
   */
  points?: ChartPoint[]
}


/**
 * 墒情一体机参数设置
 * 用于device表中当设备类型是墒情一体机的时候 misc字段保存的为该类型的数组
 *
 * SMDParamConfigVO
 */
export interface SMDParamConfig {
  /**
   * 精度
   */
  accuracy?: number
  /**
   * 对应的通道
   */
  channel?: number
  /**
   * 是否在参数统计图表中显示
   */
  chart?: boolean
  /**
   * 字段名称
   */
  columnName?: string
  /**
   * 启用
   */
  enabled?: boolean
  /**
   * 前端图标
   */
  icon?: string
  /**
   * 参数代码
   */
  paramCode?: string
  /**
   * 参数名称
   */
  paramName?: string
  /**
   * 单位
   */
  unit?: string
}

export interface WeatherParam {
  /**
     * 日期
     */
  date?: string;
  /**
   * 当天每小时相对湿度（百分比数值）
   */
  humidity?: number;
  icon?: string;
  /**
   * 当天月相名称
   */
  moonPhase?: string;
  /**
   * 当天月升时间（格式：HH:mm），可能为空
   */
  moonrise?: string;
  /**
   * 当天月落时间（格式：HH:mm），可能为空
   */
  moonset?: string;
  /**
   * 降雨量 mm
   */
  precip?: number;
  /**
   * 大气压强（单位：百帕）
   */
  pressure?: number;
  /**
   * 当天日出时间（格式：HH:mm），在高纬度地区可能为空
   */
  sunrise?: string;
  /**
   * 日落时间（格式：HH:mm），在高纬度地区可能为空
   */
  sunset?: string;
  /**
   * 最高温度
   */
  tempMax?: number;
  /**
   * 最低温度
   */
  tempMin?: number;
  /**
   * 是否是今天
   */
  today?: boolean;
}
