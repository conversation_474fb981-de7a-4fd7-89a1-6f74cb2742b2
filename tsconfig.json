{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "target": "ESNext",
    "jsx": "preserve",
    "lib": ["ESNext", "DOM"],
    "useDefineForClassFields": true,
    "baseUrl": ".",
    "module": "ESNext",
    "moduleResolution": "Node",
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["types/*"]
    },
    "resolveJsonModule": true,
    /* 允许引入js文件 */
    "allowJs": true,
    "strict": false,
    "sourceMap": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "references": [{ "path": "./tsconfig.node.json" }],
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "types/**/*.ts"],
  "exclude": ["node_modules", "dist", "**/*.js", "**/*.svg"]
}
