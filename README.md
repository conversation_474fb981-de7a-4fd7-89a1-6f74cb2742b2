# 物华大数据

## [架构文档](public/doc/index.md)

## 前端开发规范参考[这里](https://git.vankeytech.com/developers-guide/-/blob/main/frontend/FRONTEND.md)

## 项目注意点

1. 由于本项目没有登录功能,所以在开发时需要将客户端中的token复制到该[文件](src/utils/auth.ts)中,生产环境无需担心该问题,因为生产环境能获取到管理端的token
2. 项目api接口不需要添加前缀`/api/v1`,在请求时已统一添加,且将其定义在.env常量中:`VITE_APP_BASE_API`

Tip:

[客户端测试环境](https://whzl-customer.vankeytech.com:9905)

[客户端生产环境](https://csm.scwhnk.com)

## 开发环境

### node: `22.14.0`

## 项目启动

1. 安装依赖

项目是在pnpm下开发的，如果没有安装pnpm，可以使用npm或者yarn代替。如若报错，请[安装pnpm](https://www.pnpm.cn/installation)
后尝试。

```bash
 pnpm install
```

2. 启动项目

```bash
 pnpm run dev
```

3. 打包项目

```bash
 pnpm run build
```

## 该模板内置了以下内容和插件

### 一、使用 pnpm 作为包管理工具

如何安装pnpm？

方式一(推荐)：通过node.js安装，直接在终端执行：

```bash
corepack enable pnpm
```

更多方式请参考[安装](https://pnpm.io/zh/installation)

### 二、使用pinia作为状态管理工具

通过以下方式快速掌握pinia的使用：

1. [官方Demo](https://stackblitz.com/github/piniajs/example-vue-3-vite?file=src%2Fstores%2Fuser.ts)
2. [官方文档](https://pinia.vuejs.org/zh/)

### 三、添加[unocss](https://github.com/unocss/unocss) (原子化 CSS)

相关插件下载`unocss` 、`to unocss`(仅Vscode)

#### 相关文档

- [tailwind](https://tailwindcss.com/docs/installation)
- [unocss官方文档](https://unocss.dev/)
- [class速查表](https://tailwind.muzhifan.top/)

### 四、svg图标使用

使用[vite-plugin-svg-icons](https://github.com/vbenjs/vite-plugin-svg-icons)插件
使用方式：

#### 1.将svg文件放入`src/icons`文件夹下

```bash
--icons
    --icon1.svg
    --icon2.svg
    --icon3.svg
    --dir
        --icon1.svg
```

#### 2.组件中使用

```vue
<template>
  <div>
    <SvgIcon name="icon1" />
    <SvgIcon name="icon2" />
    <SvgIcon name="icon3" />
    <SvgIcon name="dir-icon1" />
  </div>
</template>
```

### 五、系统自适应

使用[postcss-px-to-viewport](https://github.com/evrone/postcss-px-to-viewport/tree/9d6c5cee5eea367fb4c7cf0c3bb1117979b5fbf4)插件，将px单位转换为vw单位，实现系统自适应

### 六、使用mitt作为事件总线

查看[官方文档](https://github.com/developit/mitt)
