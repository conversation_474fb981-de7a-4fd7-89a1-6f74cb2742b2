declare global {
  /**
   * 分页查询参数
   */
  type PageQuery<T> = Partial<T> & {
    pageNum: number
    pageSize: number
    keyword?: string
  }

  type PromiseList<T> = Promise<Result<T[]>>

  type PromiseResult<T> = Promise<Result<T>>

  type PromiseRecords<T> = Promise<Result<Records<T>>>

  interface Result<T> {
    code: number
    data: T
    msg: string
  }

  interface Records<T> {
    records: T[]
    total: number
    size: number
    current: number
    orders: any[]
    optimizeCountSql: boolean
    searchCount: boolean
    maxLimit?: any
    countId?: any
    pages: number
  }

  interface GeoPoint {
    latitude?: number
    longitude?: number

  }

  export interface FileInfo {
    /**
     * 文件名称
     */
    name?: string
    /**
     * 原始名称
     */
    originalFilename?: string
    /**
     * 大小
     */
    size?: number
    /**
     * 格式
     */
    type?: string
    /**
     * 单位
     */
    unit?: string
    /**
     * 文件URL
     */
    url?: string
  }

  declare const ZLMRTCClient: any

}

export {}
