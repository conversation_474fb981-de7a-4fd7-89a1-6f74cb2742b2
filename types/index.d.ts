import type { AttributifyAttributes } from '@unocss/preset-attributify'
import type * as echarts from 'echarts'
import type { DeviceType } from '@/constants'

// 扩展路由meta的类型
declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    icon?: string
    noMap?: boolean
    deviceType?: DeviceType
  }
}

export declare interface Fn<T = any, R = T> {
  (...arg: T[]): R
}

export declare interface AsyncFn<T = any, R = T> {
  (...arg: T[]): Promise<R>
}

declare module '@vue/runtime-dom' {
  interface HTMLAttributes extends AttributifyAttributes {}
}

export type EChartsOption = echarts.EChartsOption

// Extend the Leaflet Marker type to include a data property
declare module 'leaflet' {
  interface Marker {
    data?: Farmland
  }
  interface Polygon {
    data?: Farmland
  }
}

export interface RequestBase {
  /**
   * 设备ID
   */
  deviceId?: number
  /**
   * 地块ID
   */
  farmlandId?: number
  /**
   * years  array[integer] 查询的年份
   */
  years?: Array

  historyDays?: number
  featureDays?: number
  /**
   *  pageNum pageSize 分页参数
   */
  pageNum?: number
  pageSize?: number
  findDate?: string
}

export interface ChartData {
  x?: string
  y?: number
}
