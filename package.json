{"name": "wuhua-big-data", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@10.10.0", "engines": {"node": ">=22.14.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "prettier": "prettier --write .", "lint": "eslint .", "lint:fix": "pnpm run lint --fix", "test": "vitest --run --coverage --isolate", "style": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "svgo": "svgo -r -f ./src/icons --config ./src/icons/config.js"}, "dependencies": {"@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "axios": "1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.11", "gcoord": "^1.0.7", "highlight.js": "^11.11.1", "leaflet": "^1.9.4", "marked": "^15.0.11", "md-editor-v3": "^5.4.5", "minio-js": "^1.0.7", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "proj4": "^2.19.5", "proj4leaflet": "^1.0.2", "vue": "^3.5.13", "vue-router": "4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^4.12.1", "@types/leaflet": "^1.9.17", "@types/node": "^22.15.12", "@unocss/preset-rem-to-px": "^66.1.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/coverage-v8": "^3.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.26.0", "eslint-plugin-format": "^1.0.1", "postcss": "^8.5.3", "postcss-px-to-viewport": "^1.1.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.87.0", "stylelint": "^16.19.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^14.0.0", "svgo": "^3.3.2", "typescript": "^5.8.3", "unocss": "^66.1.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "6.3.5", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-restart": "^0.4.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vitest": "3.1.3", "vue-tsc": "^2.2.10"}}