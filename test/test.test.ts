/*
 * ---------------------------------------------
 * import {mount} from "@vue/test-utils"
 * import {test,expect,describe} from "vitest"
 * import testVue from ".vue"
 *   describe('testVue', () => {
 *    test('it should output text', () => {
 *        const wrapper = mount(testVue, {
 *            props: {
 *                number: 0,
 *            }
 *          })
 *        // 断言输出
 *        expect(wrapper.html()).toContain('0')
 *      })
 *    })
 */
